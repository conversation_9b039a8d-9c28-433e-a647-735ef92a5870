import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import './BookStore.css';

const MyOrdersNew = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);

      // Get orders from localStorage (from our new checkout process)
      const localOrders = JSON.parse(localStorage.getItem('orders') || '[]');

      // Convert local orders to the format expected by the component
      const formattedLocalOrders = localOrders.map(order => ({
        _id: order.id,
        booktitle: order.items.map(item => item.title).join(', '),
        bookauthor: order.items.map(item => item.author).join(', '),
        totalamount: order.total.toString(),
        BookingDate: new Date(order.orderDate).toLocaleDateString(),
        Delivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 7 days from now
        itemImage: order.items[0]?.itemImage || '',
        description: `Order containing ${order.items.length} book${order.items.length > 1 ? 's' : ''}`,
        city: order.customerInfo.city,
        state: order.customerInfo.state,
        isLocalOrder: true,
        originalOrder: order // Keep reference to original order data
      }));

      const user = JSON.parse(localStorage.getItem('user') || '{}');
      let backendOrders = [];

      // Try to fetch orders from backend if user is logged in
      if (user.id) {
        try {
          const response = await axios.get(`http://localhost:4000/getorders/${user.id}`);
          backendOrders = response.data;
        } catch (backendErr) {
          console.log('Backend orders not available, using local orders only');
        }
      }

      // Combine local orders with backend orders
      const allOrders = [...formattedLocalOrders, ...backendOrders];

      if (allOrders.length === 0) {
        // Show sample orders if no orders exist
        setOrders([
          {
            _id: 'sample-1',
            booktitle: 'The Alchemist',
            bookauthor: 'Paulo Coelho',
            totalamount: '299',
            BookingDate: '12/25/2024',
            Delivery: '1/1/2025',
            itemImage: '1700630708206-1979210[1].jpg',
            description: 'A philosophical novel about a young shepherd\'s journey to find treasure and discover his personal legend.',
            city: 'Mumbai',
            state: 'Maharashtra',
            isSample: true
          },
          {
            _id: 'sample-2',
            booktitle: 'The Kite Runner',
            bookauthor: 'Khaled Hosseini',
            totalamount: '349',
            BookingDate: '12/20/2024',
            Delivery: '12/27/2024',
            itemImage: '1700631424222-18505809[1].jpg',
            description: 'A powerful story of friendship, betrayal, and redemption set against the backdrop of Afghanistan\'s tumultuous history.',
            city: 'Delhi',
            state: 'Delhi',
            isSample: true
          }
        ]);
      } else {
        setOrders(allOrders);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again later.');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };



  const getOrderStatus = (bookingDate, deliveryDate) => {
    const today = new Date();
    const booking = new Date(bookingDate);
    const delivery = new Date(deliveryDate);
    
    if (today < booking) {
      return { status: 'Pending', color: 'warning' };
    } else if (today >= booking && today < delivery) {
      return { status: 'Processing', color: 'info' };
    } else {
      return { status: 'Delivered', color: 'success' };
    }
  };

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="loading"></div>
          <p>Loading your orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="text-primary">My Orders</h2>
        <button 
          className="btn btn-outline-secondary"
          onClick={() => navigate('/books')}
        >
          Continue Shopping
        </button>
      </div>

      {error && (
        <div className="alert alert-warning text-center mb-4">
          {error}
        </div>
      )}

      {orders.length === 0 ? (
        <div className="text-center py-5">
          <h4 className="text-muted">No orders found</h4>
          <p className="text-muted">You haven't placed any orders yet!</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/books')}
          >
            Start Shopping
          </button>
        </div>
      ) : (
        <div className="row">
          {orders.map(order => {
            const orderStatus = getOrderStatus(order.BookingDate, order.Delivery);
            return (
              <div key={order._id} className="col-12 mb-4">
                <div className="card">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">Order #{order._id}</h6>
                    <span className={`badge badge-${orderStatus.color}`}>
                      {orderStatus.status}
                    </span>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-2">
                        <img 
                          src={getImageUrl(order.itemImage)} 
                          className="img-fluid rounded"
                          alt={order.booktitle}
                          style={{ maxHeight: '120px', objectFit: 'cover' }}
                          onError={handleImageError}
                        />
                      </div>
                      <div className="col-md-6">
                        <h5 className="text-primary">{order.booktitle}</h5>
                        <p className="text-muted mb-1">by {order.bookauthor}</p>
                        <p className="text-muted mb-2">{order.description}</p>
                        <div className="row">
                          <div className="col-sm-6">
                            <small className="text-muted">
                              <strong>Delivery Address:</strong><br />
                              {order.city}, {order.state}
                            </small>
                          </div>
                          <div className="col-sm-6">
                            <small className="text-muted">
                              <strong>Genre:</strong> {order.bookgenre || 'Not specified'}
                            </small>
                          </div>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="text-right">
                          <h5 className="text-success mb-3">₹{order.totalamount}</h5>
                          <div className="mb-2">
                            <small className="text-muted">
                              <strong>Order Date:</strong><br />
                              {order.BookingDate}
                            </small>
                          </div>
                          <div className="mb-2">
                            <small className="text-muted">
                              <strong>Expected Delivery:</strong><br />
                              {order.Delivery}
                            </small>
                          </div>
                          <div className="mt-3">
                            <button 
                              className="btn btn-outline-primary btn-sm mr-2"
                              onClick={() => navigate(`/orderitem/${order._id}`)}
                            >
                              View Details
                            </button>
                            {orderStatus.status === 'Delivered' && (
                              <button 
                                className="btn btn-outline-secondary btn-sm"
                                onClick={() => alert('Review functionality coming soon!')}
                              >
                                Write Review
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {orders.length > 0 && (
        <div className="text-center mt-4">
          <p className="text-muted">
            Showing {orders.length} order{orders.length !== 1 ? 's' : ''}
          </p>
        </div>
      )}
    </div>
  );
};

export default MyOrdersNew;
