const mongoose = require('mongoose');
require('./db/config');

// Import the User model
const User = require('./db/Users/<USER>');

async function createDemoUser() {
  try {
    console.log('🚀 Creating demo user account...');
    
    // Demo user credentials
    const demoUserData = {
      name: 'Demo User',
      email: '<EMAIL>',
      password: 'demo123'
    };

    // Check if demo user already exists
    const existingUser = await User.findOne({ email: demoUserData.email });
    
    if (existingUser) {
      console.log('✅ Demo user already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: demo123');
      console.log('👤 Name: Demo User');
      return;
    }

    // Create new user (password stored as plain text as per existing system)
    const newUser = new User({
      name: demoUserData.name,
      email: demoUserData.email,
      password: demoUserData.password
    });

    // Save to database
    await newUser.save();

    console.log('🎉 Demo user created successfully!');
    console.log('');
    console.log('📋 Demo Account Details:');
    console.log('========================');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: demo123');
    console.log('👤 Name: Demo User');
    console.log('');
    console.log('🌐 You can now use these credentials to:');
    console.log('  • Login to the website');
    console.log('  • Browse books');
    console.log('  • Add books to cart');
    console.log('  • Complete checkout process');
    console.log('  • View order history');
    console.log('');
    console.log('🔗 Website URL: http://localhost:5174');

  } catch (error) {
    console.error('❌ Error creating demo user:', error);
    
    if (error.code === 11000) {
      console.log('ℹ️  Demo user already exists with this email');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: demo123');
    }
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}



// Run the function
console.log('🚀 Starting Demo User Creation Script...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  createDemoUser();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
