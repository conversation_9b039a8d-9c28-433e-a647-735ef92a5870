# 📚 BookStore Demo Credentials

## 🎯 Demo User Account

Use these credentials to test the complete BookStore website functionality:

### 👤 User Login
```
📧 Email: <EMAIL>
🔑 Password: demo123
👤 Name: Demo User
```

## 🌐 Website Access

**Frontend URL:** http://localhost:5174
**Backend API:** http://localhost:4000

## 🚀 How to Test the Website

### 1. **Login Process**
- Go to http://localhost:5174
- Click "Login" 
- Enter the demo credentials above
- You'll be redirected to the Welcome page

### 2. **Browse Books**
- From Welcome page, click "Browse Books"
- View the collection of 8 sample books
- Each book has real data from the backend database

### 3. **Shopping Experience**
- Click on any book to view details
- Add books to your cart
- View cart contents
- Proceed to checkout

### 4. **Checkout Process**
- Fill in customer information
- Enter payment details (demo data is fine)
- Complete the order
- View the payment success page

### 5. **Order History**
- Navigate to "My Orders" 
- View your completed orders
- See order details and status

### 6. **Logout**
- Click logout from any page
- You'll be redirected to the Wishes page
- Can login again or browse as guest

## 📋 Available Features to Test

✅ **User Authentication**
- Login/Logout functionality
- Welcome page after login
- Session management

✅ **Book Browsing**
- Home page with featured books
- Complete book catalog
- Book detail pages
- Search functionality

✅ **Shopping Cart**
- Add/remove books
- Update quantities
- Cart persistence
- Checkout process

✅ **Order Management**
- Complete checkout flow
- Payment processing simulation
- Order confirmation
- Order history viewing

✅ **Navigation**
- Responsive navigation bar
- Logout from any page
- Breadcrumb navigation
- Mobile-friendly design

## 🛠️ Technical Details

### Database Content
- **8 Sample Books** with real data
- **Demo User Account** ready for testing
- **Clean Database** (wishlist functionality removed)

### Sample Books Available
1. The Great Gatsby - F. Scott Fitzgerald (₹299)
2. To Kill a Mockingbird - Harper Lee (₹349)
3. 1984 - George Orwell (₹279)
4. Pride and Prejudice - Jane Austen (₹259)
5. The Catcher in the Rye - J.D. Salinger (₹289)
6. Harry Potter and the Philosopher's Stone - J.K. Rowling (₹399)
7. The Lord of the Rings - J.R.R. Tolkien (₹599)
8. Dune - Frank Herbert (₹449)

## 🔧 Troubleshooting

### If Login Doesn't Work
1. Make sure backend server is running on port 4000
2. Check if demo user exists in database
3. Verify frontend is running on port 5174

### If Books Don't Load
1. Ensure backend server is running
2. Check if sample books are in database
3. Verify API endpoint http://localhost:4000/books

### If Images Don't Load
1. Check if backend is serving static files
2. Verify image paths in database
3. Ensure uploads folder exists in backend

## 📞 Support

If you encounter any issues during the demo:
1. Check that both frontend and backend servers are running
2. Verify database connection is working
3. Clear browser cache if needed
4. Check browser console for any errors

---

**Happy Testing! 🎉**

This demo showcases a complete e-commerce bookstore with user authentication, shopping cart, checkout process, and order management. The system is ready for demonstration and further development.

**Last Updated:** December 27, 2024
