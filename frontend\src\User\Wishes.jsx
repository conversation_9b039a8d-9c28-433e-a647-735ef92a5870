import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaHeart, FaStar, FaQuoteLeft, FaSignInAlt, FaUserPlus } from 'react-icons/fa';

const Wishes = () => {
  const navigate = useNavigate();

  const handleLogin = () => {
    navigate('/login');
  };

  const handleSignup = () => {
    navigate('/signup');
  };

  const handleBrowseBooks = () => {
    navigate('/books');
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const inspirationalQuotes = [
    {
      quote: "A reader lives a thousand lives before he dies. The man who never reads lives only one.",
      author: "George <PERSON>"
    },
    {
      quote: "Books are a uniquely portable magic.",
      author: "<PERSON>"
    },
    {
      quote: "The more that you read, the more things you will know. The more that you learn, the more places you'll go.",
      author: "<PERSON><PERSON> <PERSON><PERSON>"
    }
  ];

  const randomQuote = inspirationalQuotes[Math.floor(Math.random() * inspirationalQuotes.length)];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FaBook className="text-purple-600 text-2xl" />
              <h1 className="text-2xl font-bold text-gray-800">BookStore</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleGoHome}
                className="text-gray-600 hover:text-gray-800 font-medium"
              >
                Home
              </button>
              <button
                onClick={handleBrowseBooks}
                className="text-purple-600 hover:text-purple-800 font-medium"
              >
                Browse Books
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Farewell Message */}
        <div className="text-center mb-12">
          <div className="mb-6">
            <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaHeart className="text-white text-3xl" />
            </div>
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              Thank you for visiting! 💜
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              We hope you found some amazing books today.
            </p>
            <p className="text-lg text-gray-500">
              Your reading journey doesn't end here!
            </p>
          </div>
        </div>

        {/* Inspirational Quote */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-12 border border-purple-100 max-w-4xl mx-auto">
          <div className="text-center">
            <FaQuoteLeft className="text-purple-400 text-3xl mx-auto mb-4" />
            <blockquote className="text-2xl font-medium text-gray-800 mb-4 italic">
              "{randomQuote.quote}"
            </blockquote>
            <cite className="text-lg text-purple-600 font-semibold">
              — {randomQuote.author}
            </cite>
          </div>
        </div>

        {/* Wishes and Dreams Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaBook className="text-white text-2xl" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-3">Keep Reading</h3>
            <p className="text-gray-600">
              May your reading list never end and your imagination always soar to new heights.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-pink-400 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaHeart className="text-white text-2xl" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-3">Follow Your Heart</h3>
            <p className="text-gray-600">
              May you always find books that speak to your soul and stories that touch your heart.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaStar className="text-white text-2xl" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-3">Dream Big</h3>
            <p className="text-gray-600">
              May every book you read inspire you to dream bigger and reach for the stars.
            </p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl shadow-xl p-8 text-white text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Ready to Continue Your Journey?</h2>
          <p className="text-xl mb-6 opacity-90">
            Join our community of book lovers and never miss out on great reads!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={handleLogin}
              className="bg-white text-purple-600 px-8 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors flex items-center space-x-2"
            >
              <FaSignInAlt />
              <span>Login</span>
            </button>
            
            <button
              onClick={handleSignup}
              className="bg-purple-700 text-white px-8 py-3 rounded-xl font-semibold hover:bg-purple-800 transition-colors flex items-center space-x-2"
            >
              <FaUserPlus />
              <span>Sign Up</span>
            </button>
          </div>
        </div>

        {/* Browse Without Account */}
        <div className="text-center">
          <p className="text-gray-600 mb-4">
            Or continue exploring our collection without an account
          </p>
          <button
            onClick={handleBrowseBooks}
            className="bg-white border-2 border-purple-300 text-purple-600 px-8 py-3 rounded-xl font-semibold hover:bg-purple-50 transition-colors"
          >
            Browse Books as Guest
          </button>
        </div>

        {/* Footer Wishes */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Our Wishes for You ✨</h3>
            <div className="space-y-3 text-gray-600">
              <p>📚 May you always find the perfect book at the perfect time</p>
              <p>🌟 May every story you read add magic to your life</p>
              <p>💫 May your love for reading continue to grow and inspire others</p>
              <p>🎯 May you achieve all your dreams, one page at a time</p>
            </div>
          </div>
        </div>

        {/* Final Message */}
        <div className="text-center mt-12">
          <p className="text-lg text-gray-500">
            Thank you for being part of our BookStore family! 💜
          </p>
          <p className="text-sm text-gray-400 mt-2">
            We'll be here whenever you're ready to discover your next great read.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Wishes;
