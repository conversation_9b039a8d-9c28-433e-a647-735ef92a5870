/* Ultra Modern Login Page Animations */

@keyframes float-up {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
}

/* Glass Morphism Effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-morphism-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Input Focus Effects */
.input-focus-glow:focus {
  box-shadow: 
    0 0 0 2px rgba(168, 85, 247, 0.3),
    0 0 20px rgba(168, 85, 247, 0.2);
}

/* Button Hover Effects */
.button-hover-lift {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.button-hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
}

.button-hover-lift:active {
  transform: translateY(0) scale(0.98);
}

/* Gradient Text Animation */
.gradient-text-animated {
  background: linear-gradient(
    45deg,
    #8b5cf6,
    #06b6d4,
    #10b981,
    #f59e0b,
    #ef4444,
    #8b5cf6
  );
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Loading Spinner */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 2px solid #ffffff;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Neon Glow Effect */
.neon-glow {
  box-shadow: 
    0 0 5px rgba(168, 85, 247, 0.4),
    0 0 10px rgba(168, 85, 247, 0.3),
    0 0 15px rgba(168, 85, 247, 0.2);
}

.neon-glow-green {
  box-shadow: 
    0 0 5px rgba(16, 185, 129, 0.4),
    0 0 10px rgba(16, 185, 129, 0.3),
    0 0 15px rgba(16, 185, 129, 0.2);
}

.neon-glow-red {
  box-shadow: 
    0 0 5px rgba(239, 68, 68, 0.4),
    0 0 10px rgba(239, 68, 68, 0.3),
    0 0 15px rgba(239, 68, 68, 0.2);
}

/* Interactive Elements */
.interactive-scale {
  transition: transform 0.2s ease;
}

.interactive-scale:hover {
  transform: scale(1.05);
}

.interactive-scale:active {
  transform: scale(0.95);
}

/* Form Validation States */
.input-valid {
  border-color: rgba(16, 185, 129, 0.5);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.input-invalid {
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* Smooth Transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #8b5cf6, #06b6d4);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #7c3aed, #0891b2);
}

/* Focus States for Accessibility */
.focus-visible:focus {
  outline: 2px solid rgba(168, 85, 247, 0.8);
  outline-offset: 2px;
}

/* Text Selection */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

/* Responsive Animations */
@media (max-width: 768px) {
  .float-animation {
    animation-duration: 4s;
  }
  
  .button-hover-lift:hover {
    transform: translateY(-1px) scale(1.01);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-morphism {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid white;
  }
  
  .text-gray-300 {
    color: white !important;
  }
  
  .text-gray-400 {
    color: #ccc !important;
  }
}
