/* Ultra Modern Home Page Animations */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.glow-animation {
  animation: glow 3s ease-in-out infinite;
}

.slide-in-up {
  animation: slideInUp 0.8s ease-out;
}

.fade-in-scale {
  animation: fadeInScale 0.6s ease-out;
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Gradient Text Animation */
.gradient-text-animated {
  background: linear-gradient(
    45deg,
    #8b5cf6,
    #06b6d4,
    #10b981,
    #f59e0b,
    #ef4444,
    #8b5cf6
  );
  background-size: 300% 300%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Particle Effect */
.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(168, 85, 247, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Hover Effects */
.card-hover-effect {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card-hover-effect:hover {
  transform: translateY(-10px) scale(1.02);
}

/* Glass Morphism Effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Neon Glow Effect */
.neon-glow {
  box-shadow: 
    0 0 5px rgba(168, 85, 247, 0.5),
    0 0 10px rgba(168, 85, 247, 0.5),
    0 0 15px rgba(168, 85, 247, 0.5),
    0 0 20px rgba(168, 85, 247, 0.5);
}

/* Pulse Animation */
.pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #8b5cf6, #06b6d4);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #7c3aed, #0891b2);
}

/* Responsive Animations */
@media (max-width: 768px) {
  .float-animation {
    animation-duration: 4s;
  }
  
  .card-hover-effect:hover {
    transform: translateY(-5px) scale(1.01);
  }
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Interactive Elements */
.interactive-element {
  cursor: pointer;
  transition: all 0.3s ease;
}

.interactive-element:hover {
  filter: brightness(1.1);
}

.interactive-element:active {
  transform: scale(0.98);
}

/* Smooth Transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* Focus States */
.focus-visible:focus {
  outline: 2px solid rgba(168, 85, 247, 0.8);
  outline-offset: 2px;
}

/* Text Selection */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: white;
}
