const mongoose = require('mongoose');
const axios = require('axios');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

async function testImageUrls() {
  try {
    console.log('🧪 Testing image URLs...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Testing ${books.length} book images`);
    
    for (const book of books) {
      console.log(`\n📖 ${book.title}`);
      console.log(`   🖼️  Image: ${book.itemImage}`);
      
      // Construct the URL (same logic as frontend)
      const encodedImage = encodeURIComponent(book.itemImage);
      const imageUrl = `http://localhost:4000/uploads/${encodedImage}`;
      console.log(`   🔗 URL: ${imageUrl}`);
      
      try {
        // Test if the URL is accessible
        const response = await axios.head(imageUrl);
        console.log(`   ✅ Status: ${response.status} - Image accessible`);
      } catch (error) {
        if (error.response) {
          console.log(`   ❌ Status: ${error.response.status} - ${error.response.statusText}`);
        } else {
          console.log(`   ❌ Error: ${error.message}`);
        }
      }
    }
    
    console.log('\n🌐 Testing backend static file serving...');
    
    // Test if uploads endpoint is working
    try {
      const response = await axios.get('http://localhost:4000/uploads/');
      console.log('✅ Uploads endpoint is accessible');
    } catch (error) {
      console.log('❌ Uploads endpoint error:', error.message);
    }
    
    console.log('\n📋 Summary:');
    console.log('   🔗 Backend URL: http://localhost:4000');
    console.log('   📁 Images served from: /uploads/');
    console.log('   🖼️  Default fallback: /default_cover.svg');
    console.log('   🔄 Frontend should now display images correctly');

  } catch (error) {
    console.error('❌ Error testing image URLs:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Image URL Test...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  testImageUrls();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
