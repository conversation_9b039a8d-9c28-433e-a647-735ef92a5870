import React from 'react';
import { FaBook, FaPhone, FaEnvelope, FaMapMarkerAlt, FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-modern-dark relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-6 py-16 relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl">
                <FaBook className="text-white text-2xl" />
              </div>
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  BookStore
                </h3>
                <p className="text-gray-400 text-sm">Your Literary Journey Starts Here</p>
              </div>
            </div>
            <p className="text-gray-300 text-lg mb-6 leading-relaxed">
              "Embark on a literary journey with our book haven – where every page turns into an adventure!
              Discover thousands of books across all genres and find your next great read."
            </p>
            <button className="btn-modern btn-primary group">
              <FaEnvelope className="mr-2 group-hover:scale-110 transition-transform" />
              Contact Us
            </button>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold text-white mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li><a href="/books" className="text-gray-400 hover:text-purple-400 transition-colors">Browse Books</a></li>
              <li><a href="/login" className="text-gray-400 hover:text-purple-400 transition-colors">Customer Login</a></li>
              <li><a href="/slogin" className="text-gray-400 hover:text-purple-400 transition-colors">Seller Portal</a></li>
              <li><a href="/alogin" className="text-gray-400 hover:text-purple-400 transition-colors">Admin Panel</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-bold text-white mb-6">Contact Info</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-gray-300">
                <FaPhone className="text-purple-400" />
                <span>127-865-586-67</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <FaEnvelope className="text-purple-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <FaMapMarkerAlt className="text-purple-400" />
                <span>BookStore HQ, Literary District</span>
              </div>
            </div>
          </div>
        </div>

        {/* Social Media & Copyright */}
        <div className="border-t border-white/10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-6 mb-4 md:mb-0">
              <a href="#" className="w-10 h-10 bg-white/10 hover:bg-purple-500/20 rounded-full flex items-center justify-center text-gray-400 hover:text-purple-400 transition-all duration-300">
                <FaFacebook />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 hover:bg-blue-500/20 rounded-full flex items-center justify-center text-gray-400 hover:text-blue-400 transition-all duration-300">
                <FaTwitter />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 hover:bg-pink-500/20 rounded-full flex items-center justify-center text-gray-400 hover:text-pink-400 transition-all duration-300">
                <FaInstagram />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 hover:bg-blue-600/20 rounded-full flex items-center justify-center text-gray-400 hover:text-blue-600 transition-all duration-300">
                <FaLinkedin />
              </a>
            </div>

            <div className="text-center md:text-right">
              <p className="text-gray-400 text-sm">
                Copyright &copy; {currentYear} By{' '}
                <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent font-semibold">
                  BookStore
                </span>
                . All Rights Reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;