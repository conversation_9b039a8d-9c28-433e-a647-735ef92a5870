import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import { FaShoppingCart, FaTrash, FaPlus, FaMinus, FaArrowLeft, FaCreditCard, FaBook } from 'react-icons/fa';
import Unavbar from './Unavbar';

const MyCart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    loadCartItems();
    setTimeout(() => setIsVisible(true), 100);
  }, []);

  const loadCartItems = () => {
    try {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      setCartItems(cart);
    } catch (error) {
      console.error('Error loading cart:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
      return;
    }

    const updatedCart = cartItems.map(item =>
      item._id === itemId ? { ...item, quantity: newQuantity } : item
    );
    
    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const removeFromCart = (itemId) => {
    const updatedCart = cartItems.filter(item => item._id !== itemId);
    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      setCartItems([]);
      localStorage.removeItem('cart');
    }
  };



  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (parseFloat(item.price) * item.quantity);
    }, 0);
  };

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      alert('Your cart is empty!');
      return;
    }

    // Navigate to checkout page
    navigate('/checkout');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-modern-dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-modern-dark">
      <Unavbar />

      <div className="container mx-auto px-6 py-8 relative z-10">
        {/* Header */}
        <div className={`flex flex-col md:flex-row justify-between items-center mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="flex items-center space-x-4 mb-4 md:mb-0">
            <button
              onClick={() => navigate('/books')}
              className="btn-modern btn-secondary flex items-center space-x-2"
            >
              <FaArrowLeft />
              <span>Continue Shopping</span>
            </button>
            <h1 className="text-4xl font-bold text-white">
              My <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">Cart</span>
            </h1>
          </div>

          {cartItems.length > 0 && (
            <button
              onClick={clearCart}
              className="btn-modern flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white"
            >
              <FaTrash />
              <span>Clear Cart</span>
            </button>
          )}
        </div>

        {cartItems.length === 0 ? (
          <div className={`text-center py-16 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="glass-card p-12 max-w-md mx-auto">
              <FaShoppingCart className="text-6xl text-gray-500 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">Your cart is empty</h3>
              <p className="text-gray-400 mb-8">Discover amazing books and add them to your cart!</p>
              <button
                onClick={() => navigate('/books')}
                className="btn-modern btn-primary group"
              >
                <FaBook className="mr-2 group-hover:scale-110 transition-transform" />
                Browse Books
              </button>
            </div>
          </div>
        ) : (
          <div className={`transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            {/* Cart Items */}
            <div className="space-y-6 mb-8">
              {cartItems.map((item, index) => (
                <div
                  key={item._id}
                  className="glass-card p-6 group hover:border-purple-400/50 transition-all duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                    {/* Book Image */}
                    <div className="flex-shrink-0">
                      <img
                        src={getImageUrl(item.itemImage)}
                        alt={item.title}
                        onError={handleImageError}
                        className="w-24 h-32 object-cover rounded-xl shadow-lg group-hover:scale-105 transition-transform"
                      />
                    </div>

                    {/* Book Details */}
                    <div className="flex-1 text-center md:text-left">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-purple-300 transition-colors">
                        {item.title}
                      </h3>
                      <p className="text-gray-400 mb-2">by {item.author}</p>
                      <p className="text-2xl font-bold text-green-400">₹{item.price}</p>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => updateQuantity(item._id, item.quantity - 1)}
                        className="w-10 h-10 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl flex items-center justify-center text-white transition-colors"
                      >
                        <FaMinus className="text-sm" />
                      </button>
                      <span className="text-xl font-bold text-white min-w-[3rem] text-center">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() => updateQuantity(item._id, item.quantity + 1)}
                        className="w-10 h-10 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl flex items-center justify-center text-white transition-colors"
                      >
                        <FaPlus className="text-sm" />
                      </button>
                    </div>

                    {/* Total Price */}
                    <div className="text-center">
                      <p className="text-sm text-gray-400 mb-1">Total</p>
                      <p className="text-2xl font-bold text-green-400">
                        ₹{(parseFloat(item.price) * item.quantity).toFixed(2)}
                      </p>
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={() => removeFromCart(item._id)}
                      className="w-10 h-10 bg-red-500/20 hover:bg-red-500/30 border border-red-400/30 rounded-xl flex items-center justify-center text-red-400 hover:text-red-300 transition-colors"
                      title="Remove from cart"
                    >
                      <FaTrash className="text-sm" />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary */}
            <div className="glass-card-strong p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Order Summary</h2>

              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center text-gray-300">
                  <span>Total Items:</span>
                  <span className="font-semibold">{cartItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                </div>
                <div className="flex justify-between items-center text-gray-300">
                  <span>Subtotal:</span>
                  <span className="font-semibold">₹{calculateTotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center text-gray-300">
                  <span>Shipping:</span>
                  <span className="font-semibold text-green-400">Free</span>
                </div>
                <div className="border-t border-white/20 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-white">Total:</span>
                    <span className="text-3xl font-bold text-green-400">₹{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <button
                onClick={proceedToCheckout}
                className="btn-modern btn-primary w-full text-xl py-4 shadow-2xl hover:shadow-purple-500/25 group"
              >
                <FaCreditCard className="mr-3 group-hover:scale-110 transition-transform" />
                Proceed to Checkout
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyCart;
