import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Bar<PERSON>hart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Link } from 'react-router-dom';
import { FaUsers, FaStore, FaBook, FaShoppingCart, FaChartBar, FaTrendingUp } from 'react-icons/fa';
import Anavbar from './Anavbar';
import '../styles/ModernDesignSystem.css';

// Ultra Modern Admin Dashboard for Book Store
function Ahome() {
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch all data concurrently
        const [usersRes, vendorsRes, booksRes, ordersRes] = await Promise.all([
          axios.get('http://localhost:4000/users').catch(() => ({ data: [] })),
          axios.get('http://localhost:4000/sellers').catch(() => ({ data: [] })),
          axios.get('http://localhost:4000/item').catch(() => ({ data: [] })),
          axios.get('http://localhost:4000/orders').catch(() => ({ data: [] }))
        ]);

        setUsers(usersRes.data);
        setVendors(vendorsRes.data);
        setBooks(booksRes.data);
        setOrders(ordersRes.data);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
      } finally {
        setLoading(false);
        setTimeout(() => setIsVisible(true), 100);
      }
    };

    fetchData();
  }, []);

  const totalUsers = users.length;
  const totalVendors = vendors.length;
  const totalBooks = books.length;
  const totalOrders = orders.length;

  const data = [
    { name: 'Users', value: totalUsers, fill: '#ef4444' },
    { name: 'Vendors', value: totalVendors, fill: '#3b82f6' },
    { name: 'Books', value: totalBooks, fill: '#10b981' },
    { name: 'Orders', value: totalOrders, fill: '#f59e0b' },
  ];

  const statsCards = [
    { title: 'Total Users', value: totalUsers, icon: FaUsers, color: 'from-red-500 to-red-600', link: '/users' },
    { title: 'Vendors', value: totalVendors, icon: FaStore, color: 'from-blue-500 to-blue-600', link: '/sellers' },
    { title: 'Books', value: totalBooks, icon: FaBook, color: 'from-green-500 to-green-600', link: '/items' },
    { title: 'Orders', value: totalOrders, icon: FaShoppingCart, color: 'from-orange-500 to-orange-600', link: '/orders' },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-modern-dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-modern-dark">
      <Anavbar />

      <div className="container mx-auto px-6 py-8 relative z-10">
        {/* Header */}
        <div className={`text-center mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h1 className="text-5xl font-bold text-white mb-4">
            Admin <span className="bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">Dashboard</span>
          </h1>
          <p className="text-xl text-gray-300">Manage your BookStore platform</p>
        </div>

        {/* Stats Cards */}
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {statsCards.map((card, index) => (
            <Link key={card.title} to={card.link} className="group">
              <div className="modern-card p-6 text-center group-hover:border-red-400/50 transition-all duration-300">
                <div className={`w-16 h-16 bg-gradient-to-r ${card.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                  <card.icon className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-red-300 transition-colors">
                  {card.title}
                </h3>
                <p className="text-3xl font-bold text-red-400">{card.value}</p>
              </div>
            </Link>
          ))}
        </div>

        {/* Analytics Chart */}
        <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="glass-card-strong p-8">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold text-white flex items-center">
                <FaChartBar className="mr-3 text-red-400" />
                Platform Analytics
              </h2>
              <div className="flex items-center text-green-400">
                <FaTrendingUp className="mr-2" />
                <span className="text-sm">Growing</span>
              </div>
            </div>

            <div className="bg-white/5 rounded-2xl p-6">
              <BarChart width={800} height={400} data={data} className="mx-auto">
                <XAxis dataKey="name" stroke="#ffffff" />
                <YAxis stroke="#ffffff" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '12px',
                    color: '#ffffff'
                  }}
                />
                <Legend />
                <Bar dataKey="value" fill="#ef4444" barSize={60} radius={[4, 4, 0, 0]} />
              </BarChart>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`mt-12 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link to="/users" className="glass-card p-6 text-center group hover:border-red-400/50 transition-all duration-300">
              <FaUsers className="text-4xl text-red-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
              <h3 className="text-lg font-semibold text-white mb-2">Manage Users</h3>
              <p className="text-gray-400 text-sm">View and manage customer accounts</p>
            </Link>

            <Link to="/sellers" className="glass-card p-6 text-center group hover:border-blue-400/50 transition-all duration-300">
              <FaStore className="text-4xl text-blue-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
              <h3 className="text-lg font-semibold text-white mb-2">Manage Vendors</h3>
              <p className="text-gray-400 text-sm">Oversee seller accounts and activities</p>
            </Link>

            <Link to="/items" className="glass-card p-6 text-center group hover:border-green-400/50 transition-all duration-300">
              <FaBook className="text-4xl text-green-400 mx-auto mb-4 group-hover:scale-110 transition-transform" />
              <h3 className="text-lg font-semibold text-white mb-2">Manage Books</h3>
              <p className="text-gray-400 text-sm">Control book inventory and listings</p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Ahome;
