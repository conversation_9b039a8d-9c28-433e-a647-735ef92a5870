const mongoose = require('mongoose');
require('./db/config');

// Import the User model
const User = require('./db/Users/<USER>');

async function checkDemoUser() {
  try {
    console.log('🔍 Checking demo user in database...');
    
    // Find the demo user
    const demoUser = await User.findOne({ email: '<EMAIL>' });
    
    if (demoUser) {
      console.log('✅ Demo user found!');
      console.log('📋 User Details:');
      console.log(`  🆔 ID: ${demoUser._id}`);
      console.log(`  👤 Name: ${demoUser.name}`);
      console.log(`  📧 Email: ${demoUser.email}`);
      console.log(`  🔑 Password: ${demoUser.password}`);
    } else {
      console.log('❌ Demo user not found!');
      console.log('💡 Creating demo user...');
      
      const newUser = new User({
        name: 'Demo User',
        email: '<EMAIL>',
        password: 'demo123'
      });
      
      await newUser.save();
      console.log('✅ Demo user created successfully!');
    }
    
    // List all users
    const allUsers = await User.find();
    console.log(`\n📊 Total users in database: ${allUsers.length}`);
    allUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.email})`);
    });

  } catch (error) {
    console.error('❌ Error checking demo user:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Demo User Check...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  checkDemoUser();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
