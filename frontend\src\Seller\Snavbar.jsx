// Ultra Modern Seller Navigation Component

import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { FaBook, FaHome, FaPlus, FaBoxes, FaShoppingCart, FaStore, FaSignOutAlt, FaBars, FaTimes } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Snavbar = () => {
  const navigate = useNavigate();
  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name;
    } catch (e) {
      userName = '';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/');
  };

  return (
    <Navbar bg="primary" variant="dark" expand="lg" style={{ backgroundColor: "#003366" }}>
      <Container>
        <Navbar.Brand>
          <Link to='/shome' style={{ color: "white", textDecoration: "none", fontWeight: "bold", fontSize: "1.5rem" }}>
            <img
              src="/bookstore-logo.png"
              alt="BookStore Logo"
              style={{ width: "40px", marginRight: "10px", verticalAlign: "middle" }}
              onError={e => { e.target.style.display = 'none'; }}
            />
            BookStore <span style={{ fontSize: "1rem", fontWeight: "normal" }}>(Seller)</span>
          </Link>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="ms-auto" style={{ alignItems: "center" }}>
            <Link to="/shome" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Home</Link>
            <Link to="/myproducts" style={{ padding: "10px", color: "white", textDecoration: "none" }}>My Products</Link>
            <Link to="/addbook" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Add Book</Link>
            <Link to="/orders" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Orders</Link>
            <Button
              variant="outline-light"
              size="sm"
              style={{ marginLeft: "15px" }}
              onClick={handleLogout}
            >
              Logout
            </Button>
            {userName && (
              <span style={{ color: "white", marginLeft: "15px", fontWeight: "bold" }}>
                ({userName})
              </span>
            )}
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Snavbar;
