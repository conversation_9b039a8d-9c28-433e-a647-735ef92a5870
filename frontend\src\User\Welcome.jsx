import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaHeart, FaShoppingCart, FaUser, FaSignOutAlt, FaStar, FaArrowRight, FaHome, FaGift, FaRocket } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Welcome = () => {
  const [user, setUser] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        // Trigger entrance animation
        setTimeout(() => setIsVisible(true), 100);
      } catch (error) {
        console.error('Error parsing user data:', error);
        navigate('/login');
      }
    } else {
      // If no user data, redirect to login
      navigate('/login');
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('cart'); // Clear cart on logout
    navigate('/wishes');
  };

  const handleBrowseBooks = () => {
    navigate('/books');
  };



  const handleGoToCart = () => {
    navigate('/mycart');
  };

  const handleGoToOrders = () => {
    navigate('/myorders-new');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-modern-dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-modern-dark">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-2000"></div>
      </div>

      {/* Ultra Modern Header */}
      <header className="modern-nav sticky top-0 z-50 relative">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
                <FaBook className="text-white text-xl" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">BookStore</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-white">
                <FaUser className="text-purple-400" />
                <span className="font-medium">{user.name || user.email}</span>
              </div>
              <button
                onClick={handleLogout}
                className="btn-modern flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-red-500/25"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-16 relative z-10">
        {/* Welcome Section */}
        <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="mb-8">
            <div className="w-32 h-32 bg-gradient-to-r from-purple-500 to-blue-500 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-float shadow-2xl">
              <FaUser className="text-white text-4xl" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-4 animate-fade-in-up">
              Welcome back, <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">{user.name || user.email.split('@')[0]}</span>! 👋
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-2xl mx-auto">
              Ready to discover your next great read? Your literary journey continues here.
            </p>
          </div>

          {/* Stats Cards */}
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 max-w-5xl mx-auto transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="modern-card text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <FaBook className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">16 Books Available</h3>
              <p className="text-gray-300">Curated collection of amazing reads</p>
            </div>

            <div className="modern-card text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <FaShoppingCart className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Easy Shopping</h3>
              <p className="text-gray-300">Secure and lightning-fast checkout</p>
            </div>

            <div className="modern-card text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <FaStar className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Premium Experience</h3>
              <p className="text-gray-300">Personalized recommendations just for you</p>
            </div>
          </div>

          {/* Main Action Button */}
          <div className={`mb-12 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <button
              onClick={handleBrowseBooks}
              className="btn-modern btn-primary text-xl px-16 py-5 shadow-2xl hover:shadow-purple-500/25 group"
            >
              <FaRocket className="mr-3 group-hover:scale-110 transition-transform" />
              Start Browsing Books
              <FaArrowRight className="ml-3 group-hover:translate-x-2 transition-transform" />
            </button>
          </div>

          {/* Quick Actions */}
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <button
              onClick={handleGoToCart}
              className="glass-card p-6 text-center group hover:border-green-400/50 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <FaShoppingCart className="text-white text-xl" />
              </div>
              <span className="text-lg font-semibold text-white">My Cart</span>
              <p className="text-gray-400 text-sm mt-2">View your selected books</p>
            </button>

            <button
              onClick={handleGoToOrders}
              className="glass-card p-6 text-center group hover:border-yellow-400/50 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <FaStar className="text-white text-xl" />
              </div>
              <span className="text-lg font-semibold text-white">My Orders</span>
              <p className="text-gray-400 text-sm mt-2">Track your purchases</p>
            </button>

            <button
              onClick={() => navigate('/uhome')}
              className="glass-card p-6 text-center group hover:border-blue-400/50 transition-all duration-300"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <FaHome className="text-white text-xl" />
              </div>
              <span className="text-lg font-semibold text-white">Book Home</span>
              <p className="text-gray-400 text-sm mt-2">Explore featured books</p>
            </button>
          </div>
        </div>

        {/* Featured Section */}
        <div className={`mt-20 transition-all duration-1000 delay-900 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="glass-card-strong p-10 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10"></div>
            <div className="relative z-10">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Ready to dive into your next adventure?</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="glass-card p-8 group hover:border-blue-400/50 transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <FaBook className="text-white text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4 text-center">📚 Discover New Worlds</h3>
                  <p className="text-gray-300 mb-6 text-center">Explore our curated collection of 16 amazing books across all genres and find your next favorite read.</p>
                  <div className="text-center">
                    <button
                      onClick={handleBrowseBooks}
                      className="btn-modern btn-primary group/btn"
                    >
                      <FaRocket className="mr-2 group-hover/btn:scale-110 transition-transform" />
                      Start Exploring
                    </button>
                  </div>
                </div>

                <div className="glass-card p-8 group hover:border-green-400/50 transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <FaShoppingCart className="text-white text-2xl" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4 text-center">🛒 Complete Your Purchase</h3>
                  <p className="text-gray-300 mb-6 text-center">Review the books in your cart and proceed to our secure checkout when you're ready!</p>
                  <div className="text-center">
                    <button
                      onClick={handleGoToCart}
                      className="btn-modern btn-success group/btn"
                    >
                      <FaShoppingCart className="mr-2 group-hover/btn:scale-110 transition-transform" />
                      View Cart
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Message */}
        <div className={`text-center mt-16 transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="glass-card p-6 max-w-2xl mx-auto">
            <p className="text-gray-300 text-lg">
              <FaGift className="inline mr-2 text-purple-400" />
              Happy reading! 📖 Your literary journey awaits. Need help? Our support team is here for you.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Welcome;
