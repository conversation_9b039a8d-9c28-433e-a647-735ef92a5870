import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaB<PERSON>, <PERSON>aH<PERSON>t, FaShoppingCart, FaUser, FaSignOutAlt, FaStar } from 'react-icons/fa';

const Welcome = () => {
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
      } catch (error) {
        console.error('Error parsing user data:', error);
        navigate('/login');
      }
    } else {
      // If no user data, redirect to login
      navigate('/login');
    }
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('cart'); // Clear cart on logout
    navigate('/wishes');
  };

  const handleBrowseBooks = () => {
    navigate('/books');
  };

  const handleGoToWishlist = () => {
    navigate('/wishlist');
  };

  const handleGoToCart = () => {
    navigate('/mycart');
  };

  const handleGoToOrders = () => {
    navigate('/myorders-new');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FaBook className="text-teal-600 text-2xl" />
              <h1 className="text-2xl font-bold text-gray-800">BookStore</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600">
                <FaUser className="text-teal-600" />
                <span className="font-medium">{user.name || user.email}</span>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        {/* Welcome Section */}
        <div className="text-center mb-12">
          <div className="mb-6">
            <div className="w-24 h-24 bg-gradient-to-r from-teal-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaUser className="text-white text-3xl" />
            </div>
            <h1 className="text-4xl font-bold text-gray-800 mb-2">
              Welcome back, {user.name || user.email.split('@')[0]}! 👋
            </h1>
            <p className="text-xl text-gray-600">
              Ready to discover your next great read?
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <FaBook className="text-blue-600 text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Explore Books</h3>
              <p className="text-gray-600 text-sm">Thousands of books waiting for you</p>
            </div>
            
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-lg mx-auto mb-4">
                <FaHeart className="text-red-600 text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Your Wishlist</h3>
              <p className="text-gray-600 text-sm">Save books for later</p>
            </div>
            
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <FaShoppingCart className="text-green-600 text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Easy Shopping</h3>
              <p className="text-gray-600 text-sm">Secure and fast checkout</p>
            </div>
          </div>

          {/* Main Action Button */}
          <div className="mb-8">
            <button
              onClick={handleBrowseBooks}
              className="bg-gradient-to-r from-teal-500 to-blue-600 text-white px-12 py-4 rounded-xl text-xl font-semibold hover:from-teal-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <FaBook className="inline mr-3" />
              Browse Books
            </button>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <button
              onClick={handleGoToWishlist}
              className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-all duration-200 hover:shadow-md"
            >
              <FaHeart className="text-red-500 text-2xl mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700">Wishlist</span>
            </button>
            
            <button
              onClick={handleGoToCart}
              className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-all duration-200 hover:shadow-md"
            >
              <FaShoppingCart className="text-green-500 text-2xl mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700">My Cart</span>
            </button>
            
            <button
              onClick={handleGoToOrders}
              className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-all duration-200 hover:shadow-md"
            >
              <FaStar className="text-yellow-500 text-2xl mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700">My Orders</span>
            </button>
            
            <button
              onClick={() => navigate('/uhome')}
              className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-4 text-center transition-all duration-200 hover:shadow-md"
            >
              <FaBook className="text-blue-500 text-2xl mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-700">Home</span>
            </button>
          </div>
        </div>

        {/* Featured Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">What would you like to do today?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">📚 Discover New Books</h3>
              <p className="text-gray-600 mb-4">Explore our vast collection of books across all genres and find your next favorite read.</p>
              <button
                onClick={handleBrowseBooks}
                className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              >
                Start Browsing
              </button>
            </div>
            
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">❤️ Check Your Wishlist</h3>
              <p className="text-gray-600 mb-4">Review the books you've saved and maybe it's time to add some to your cart!</p>
              <button
                onClick={handleGoToWishlist}
                className="bg-purple-500 text-white px-6 py-2 rounded-lg hover:bg-purple-600 transition-colors"
              >
                View Wishlist
              </button>
            </div>
          </div>
        </div>

        {/* Footer Message */}
        <div className="text-center mt-12">
          <p className="text-gray-500">
            Happy reading! 📖 If you need any help, feel free to contact our support team.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Welcome;
