import React, { useState, useEffect } from 'react';
import Unavbar from './Unavbar';
import "./uhome.css";
import { Card, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import Footer from '../Componenets/Footer';



const BookCard = ({ book }) => (
  <Card style={{ width: '18rem', marginRight: "40px", marginBottom: "30px" }}>
    <Link to={`/uitem/${book._id}`}>
      <Card.Img
        variant="top"
        src={getImageUrl(book.itemImage)}
        alt={book.title}
        onError={handleImageError}
        style={{ height: "350px", objectFit: "cover" }}
      />
    </Link>
    <Card.Body>
      <Card.Title className='text-center'>{book.title}</Card.Title>
      <Card.Text className='text-center'>
        <span style={{ fontSize: "15px", color: "#555" }}>{book.author}</span><br />
        <span style={{ fontWeight: "bold", color: "#2d8659" }}>₹{book.price}</span>
      </Card.Text>
      <div className="d-flex justify-content-center">
        <Link to={`/uitem/${book._id}`}>
          <Button variant="primary" size="sm">View Details</Button>
        </Link>
        <Button variant="success" size="sm" style={{ marginLeft: "10px" }}>Add to Cart</Button>
      </div>
    </Card.Body>
  </Card>
);

const Uhome = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:4000/books');
      setBooks(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching books:', err);
      setError('Failed to load books. Please try again later.');
      setBooks([]);
    } finally {
      setLoading(false);
    }
  };

  // Split books into featured and recommendations
  const featuredBooks = books.slice(0, 4);
  const recommendedBooks = books.slice(4, 8);

  if (loading) {
    return (
      <div>
        <Unavbar />
        <div className="container mt-4">
          <div className="text-center" style={{ padding: "4rem 0" }}>
            <div className="spinner-border text-primary" role="status" style={{ width: "3rem", height: "3rem" }}>
              <span className="visually-hidden">Loading...</span>
            </div>
            <h3 className="mt-3">Loading amazing books for you...</h3>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Unavbar />
        <div className="container mt-4">
          <div className="text-center" style={{ padding: "4rem 0" }}>
            <h3 className="text-danger mb-3">{error}</h3>
            <Button variant="primary" onClick={fetchBooks}>
              Try Again
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Unavbar />
      <div className="container mt-4">
        <h1 className='text-center mb-4' style={{ fontSize: "50px" }}>Featured Books</h1>
        <div className="d-flex flex-wrap justify-content-center">
          {featuredBooks.length > 0 ? (
            featuredBooks.map((book) => (
              <BookCard book={book} key={book._id} />
            ))
          ) : (
            <p className="text-center">No featured books available.</p>
          )}
        </div>

        {recommendedBooks.length > 0 && (
          <>
            <h1 className='text-center my-4' style={{ fontSize: "50px" }}>Top Recommendations</h1>
            <div className="d-flex flex-wrap justify-content-center">
              {recommendedBooks.map((book) => (
                <BookCard book={book} key={book._id} />
              ))}
            </div>
          </>
        )}

        <div className="text-center mt-5">
          <Link to="/books">
            <Button variant="outline-dark" size="lg">Browse All Books</Button>
          </Link>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Uhome;