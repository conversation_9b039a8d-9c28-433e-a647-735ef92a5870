const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

// Complete book data matching the 16 images in uploads folder
const completeBookData = [
  {
    title: "1984",
    author: "<PERSON>",
    genre: "Dystopian Fiction",
    description: "A dystopian social science fiction novel about totalitarian control and surveillance in a society where independent thinking is a crime.",
    price: "299",
    itemImage: "1984 by <PERSON>.j<PERSON>",
    userName: "BookStore Admin"
  },
  {
    title: "A Thousand Splendid Suns",
    author: "<PERSON><PERSON><PERSON>",
    genre: "Historical Fiction",
    description: "A powerful story of two women in Afghanistan whose lives become intertwined through friendship, family, and survival.",
    price: "399",
    itemImage: "A Thousand Splendid Suns.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "Atomic Habits",
    author: "James Clear",
    genre: "Self-Help",
    description: "An easy and proven way to build good habits and break bad ones through tiny changes that deliver remarkable results.",
    price: "449",
    itemImage: "Atomic Habits.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A memoir about a woman who grows up in a survivalist family and eventually earns a PhD from Cambridge University.",
    price: "379",
    itemImage: "Educated.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "Life of Pi",
    author: "Yann Martel",
    genre: "Adventure Fiction",
    description: "A philosophical novel about a young Indian boy stranded on a lifeboat in the Pacific Ocean with a Bengal tiger.",
    price: "329",
    itemImage: "Life of Pi.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "One Hundred Years of Solitude",
    author: "Gabriel García Márquez",
    genre: "Magical Realism",
    description: "A multi-generational saga of the Buendía family in the fictional town of Macondo, blending reality with fantasy.",
    price: "419",
    itemImage: "One Hundred Years of Solitude.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Alchemist",
    author: "Paulo Coelho",
    genre: "Philosophical Fiction",
    description: "A philosophical book about a young Andalusian shepherd who travels from Spain to Egypt in search of treasure.",
    price: "279",
    itemImage: "The Alchemist.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Book Thief",
    author: "Markus Zusak",
    genre: "Historical Fiction",
    description: "Set in Nazi Germany, this novel tells the story of a young girl living with foster parents who steals books.",
    price: "359",
    itemImage: "The Book Thief.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    genre: "Coming-of-age Fiction",
    description: "A controversial novel about teenage rebellion and alienation, following Holden Caulfield's experiences in New York City.",
    price: "289",
    itemImage: "The Catcher in the Rye.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A mystery thriller that follows symbologist Robert Langdon as he investigates a murder in the Louvre Museum.",
    price: "349",
    itemImage: "The Da Vinci Code.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Romance",
    description: "A heart-wrenching love story between two teenagers who meet at a cancer support group.",
    price: "319",
    itemImage: "The Fault in Our Stars.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Girl with the Dragon Tattoo",
    author: "Stieg Larsson",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a wealthy family's dark secrets.",
    price: "389",
    itemImage: "The Girl with the Dragon Tattoo.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Great Gatsby",
    author: "F. Scott Fitzgerald",
    genre: "Classic Literature",
    description: "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream.",
    price: "299",
    itemImage: "The Great Gatsby.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Young Adult",
    description: "A dystopian novel about a televised fight to the death in a post-apocalyptic society.",
    price: "339",
    itemImage: "The Hunger Games.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "The Kite Runner",
    author: "Khaled Hosseini",
    genre: "Historical Fiction",
    description: "A powerful story of friendship, guilt, and redemption set against the backdrop of Afghanistan's tumultuous history.",
    price: "369",
    itemImage: "The Kite Runner.jpeg",
    userName: "BookStore Admin"
  },
  {
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    genre: "Classic Literature",
    description: "A classic novel about racial injustice and moral growth in the American South during the 1930s.",
    price: "309",
    itemImage: "To Kill a Mockingbird.jpeg",
    userName: "BookStore Admin"
  }
];

async function create16Books() {
  try {
    console.log('📚 Creating complete book database with 16 books...');
    
    // Clear existing books
    console.log('🗑️  Clearing existing books...');
    await items.deleteMany({});
    console.log('✅ Existing books cleared');
    
    // Add all 16 books
    console.log('\n📖 Adding 16 complete books...');
    
    for (const bookData of completeBookData) {
      const newBook = new items(bookData);
      await newBook.save();
      console.log(`✅ Added: ${bookData.title} by ${bookData.author}`);
    }
    
    console.log(`\n🎉 Successfully added all 16 books!`);
    
    // Verify the final database
    const allBooks = await items.find();
    
    console.log(`\n📋 Complete Book Catalog (${allBooks.length} books):`);
    console.log('='.repeat(80));
    
    allBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   👤 Author: ${book.author}`);
      console.log(`   📚 Genre: ${book.genre}`);
      console.log(`   💰 Price: ₹${book.price}`);
      console.log(`   🖼️  Image: ${book.itemImage}`);
      console.log('');
    });
    
    console.log('✅ All 16 books now have proper details and matching images!');
    console.log('🌐 Your BookStore now has a complete catalog');
    console.log('🔄 Refresh your browser to see all the new books with images');

  } catch (error) {
    console.error('❌ Error creating book database:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting 16 Books Creation...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  create16Books();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
