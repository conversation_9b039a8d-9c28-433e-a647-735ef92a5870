const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

async function checkImages() {
  try {
    console.log('🖼️  Checking image setup...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Found ${books.length} books in database`);
    
    console.log('\n📋 Book Image Details:');
    books.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   📁 Image: ${book.itemImage}`);
      console.log(`   🔗 Expected URL: http://localhost:4000/uploads/${book.itemImage}`);
    });
    
    // Check uploads directory
    console.log('\n📂 Checking uploads directory...');
    const uploadsPath = path.join(__dirname, 'uploads');
    
    if (fs.existsSync(uploadsPath)) {
      console.log('✅ Uploads directory exists');
      const files = fs.readdirSync(uploadsPath);
      console.log(`📁 Files in uploads: ${files.length}`);
      
      if (files.length > 0) {
        console.log('📄 Files found:');
        files.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file}`);
        });
      } else {
        console.log('⚠️  No files in uploads directory');
      }
    } else {
      console.log('❌ Uploads directory does not exist');
      console.log('💡 Creating uploads directory...');
      fs.mkdirSync(uploadsPath, { recursive: true });
      console.log('✅ Uploads directory created');
    }
    
    // Check public directory for book covers
    console.log('\n📂 Checking public directory...');
    const publicPath = path.join(__dirname, '..', 'frontend', 'public');
    
    if (fs.existsSync(publicPath)) {
      console.log('✅ Public directory exists');
      const files = fs.readdirSync(publicPath);
      const imageFiles = files.filter(file => 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg') || 
        file.toLowerCase().endsWith('.png')
      );
      
      console.log(`🖼️  Image files in public: ${imageFiles.length}`);
      if (imageFiles.length > 0) {
        console.log('📄 Image files found:');
        imageFiles.forEach((file, index) => {
          console.log(`   ${index + 1}. ${file}`);
        });
      }
    } else {
      console.log('❌ Public directory not found');
    }
    
    // Check server static file serving
    console.log('\n🌐 Server Configuration:');
    console.log('   📍 Backend URL: http://localhost:4000');
    console.log('   📁 Uploads path: /uploads/');
    console.log('   📁 Public path: /public/');
    
    console.log('\n🔧 Recommended Solutions:');
    console.log('1. Add default book cover images to public folder');
    console.log('2. Update database with correct image paths');
    console.log('3. Ensure server serves static files correctly');
    console.log('4. Add fallback image handling');

  } catch (error) {
    console.error('❌ Error checking images:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Image Check...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  checkImages();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
