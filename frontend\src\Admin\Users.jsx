import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useNavigate, useParams } from 'react-router-dom';

// Book data (cover images should be in public/covers/)
const defaultBooks = [
  {
    id: 1,
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    price: 12.99,
    cover: "/covers/gatsby.jpg",
    description: "A classic novel of the Roaring Twenties."
  },
  {
    id: 2,
    title: "1984",
    author: "<PERSON>",
    price: 10.99,
    cover: "/covers/1984.jpg",
    description: "A dystopian social science fiction novel."
  },
  {
    id: 3,
    title: "To Kill a Mockingbird",
    author: "<PERSON> Lee",
    price: 14.99,
    cover: "/covers/mockingbird.jpg",
    description: "A novel about racial injustice in the Deep South."
  },
  {
    id: 4,
    title: "<PERSON> and the Sorcerer's Stone",
    author: "<PERSON><PERSON><PERSON><PERSON>",
    price: 19.99,
    cover: "/covers/hp1.jpg",
    description: "The first book in the <PERSON> Potter series."
  }
];

// Navbar
function BookStoreNavbar() {
  return (
    <nav className="bg-purple-800 p-4 text-white flex justify-between items-center">
      <div className="text-2xl font-bold">Book Store</div>
      <div>
        <Link to="/" className="mr-4 text-white hover:underline">Home</Link>
        <Link to="/wishlist" className="text-white hover:underline">Wishlist</Link>
        <Link to="/admin" className="ml-4 text-white hover:underline">Admin</Link>
      </div>
    </nav>
  );
}

// Book Card
function BookCard({ book }) {
  const navigate = useNavigate();
  return (
    <div className="bg-white p-4 rounded shadow flex flex-col">
      <img
        src={book.cover}
        alt={book.title}
        className="rounded-t-lg mb-4 object-cover"
        style={{ height: '350px', width: '100%', objectFit: 'cover' }}
        onError={e => { e.target.onerror = null; e.target.src = '/covers/default.jpg'; }}
      />
      <div className="flex-1 flex flex-col">
        <p className="text-xl font-bold mb-1">{book.title}</p>
        <p className="text-gray-700 mb-1">Author: {book.author}</p>
        <p className="text-blue-500 font-bold mb-3">Price: ${book.price}</p>
        <div className="flex flex-row items-center mt-auto">
          <button
            className="bg-purple-800 text-white px-3 py-1 rounded"
            onClick={() => navigate(`/book/${book.id}`)}
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
}

// Home Page (Book Store)
function BookStore() {
  const [books, setBooks] = useState([]);

  useEffect(() => {
    // Simulate fetching books (could be replaced with API)
    setBooks(defaultBooks);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100">
      <BookStoreNavbar />
      <div className="container mx-auto p-8">
        <h2 className="text-3xl font-semibold mb-8 text-center">Book Store</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {books.map(book => (
            <BookCard
              key={book.id}
              book={book}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

// Book Details Page
function BookDetails() {
  const { id } = useParams();
  const [book, setBook] = useState(null);

  useEffect(() => {
    // Simulate fetch
    const found = defaultBooks.find(b => b.id === Number(id));
    setBook(found);
  }, [id]);

  if (!book) return <div className="p-8">Book not found.</div>;

  return (
    <div className="min-h-screen bg-gray-100">
      <BookStoreNavbar />
      <div className="container mx-auto p-8 flex flex-col md:flex-row gap-8">
        <img
          src={book.cover}
          alt={book.title}
          className="rounded-lg object-cover"
          style={{ width: 300, height: 400, objectFit: 'cover' }}
          onError={e => { e.target.onerror = null; e.target.src = '/covers/default.jpg'; }}
        />
        <div>
          <h2 className="text-3xl font-bold mb-2">{book.title}</h2>
          <p className="text-lg mb-2">by {book.author}</p>
          <p className="text-blue-600 font-bold text-xl mb-4">${book.price}</p>
          <p className="mb-4">{book.description}</p>
          <Link to="/" className="text-purple-800 hover:underline">Back to Store</Link>
        </div>
      </div>
    </div>
  );
}

// Wishlist Page
function Wishlist() {
  const [wishlist, setWishlist] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem('wishlist')) || [];
    } catch {
      return [];
    }
  });

  const books = defaultBooks.filter(b => wishlist.includes(b.id));

  const removeFromWishlist = (bookId) => {
    const updated = wishlist.filter(id => id !== bookId);
    setWishlist(updated);
    localStorage.setItem('wishlist', JSON.stringify(updated));
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <BookStoreNavbar />
      <div className="container mx-auto p-8">
        <h2 className="text-3xl font-semibold mb-8 text-center">My Wishlist</h2>
        {books.length === 0 ? (
          <p className="text-center text-gray-600">Your wishlist is empty.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {books.map(book => (
              <div key={book.id} className="bg-white p-4 rounded shadow flex flex-col">
                <img
                  src={book.cover}
                  alt={book.title}
                  className="rounded-t-lg mb-4 object-cover"
                  style={{ height: '350px', width: '100%', objectFit: 'cover' }}
                  onError={e => { e.target.onerror = null; e.target.src = '/covers/default.jpg'; }}
                />
                <div className="flex-1 flex flex-col">
                  <p className="text-xl font-bold mb-1">{book.title}</p>
                  <p className="text-gray-700 mb-1">Author: {book.author}</p>
                  <p className="text-blue-500 font-bold mb-3">Price: ${book.price}</p>
                  <button
                    className="bg-red-600 text-white px-3 py-1 rounded mt-auto"
                    onClick={() => removeFromWishlist(book.id)}
                  >
                    Remove from Wishlist
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Admin Page (Add Book)
function Admin() {
  const [adminBooks, setAdminBooks] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem('adminBooks')) || [];
    } catch {
      return [];
    }
  });
  const [title, setTitle] = useState('');
  const [author, setAuthor] = useState('');
  const [price, setPrice] = useState('');
  const [cover, setCover] = useState('');
  const [description, setDescription] = useState('');

  const handleAddBook = (e) => {
    e.preventDefault();
    if (!title || !author || !price || !cover || !description) {
      alert('Please fill all fields');
      return;
    }
    const newBook = {
      id: Date.now(),
      title,
      author,
      price: parseFloat(price),
      cover,
      description
    };
    const updatedBooks = [...adminBooks, newBook];
    setAdminBooks(updatedBooks);
    localStorage.setItem('adminBooks', JSON.stringify(updatedBooks));
    setTitle('');
    setAuthor('');
    setPrice('');
    setCover('');
    setDescription('');
    alert('Book added!');
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <BookStoreNavbar />
      <h2 className="text-2xl font-bold mb-6">Admin Dashboard</h2>
      <form className="bg-white p-6 rounded shadow mb-8 max-w-lg" onSubmit={handleAddBook}>
        <h3 className="text-lg font-semibold mb-4">Add New Book</h3>
        <input
          className="w-full mb-2 p-2 border rounded"
          type="text"
          placeholder="Title"
          value={title}
          required
          onChange={e => setTitle(e.target.value)}
        />
        <input
          className="w-full mb-2 p-2 border rounded"
          type="text"
          placeholder="Author"
          value={author}
          required
          onChange={e => setAuthor(e.target.value)}
        />
        <input
          className="w-full mb-2 p-2 border rounded"
          type="number"
          placeholder="Price"
          value={price}
          required
          min="0"
          step="0.01"
          onChange={e => setPrice(e.target.value)}
        />
        <input
          className="w-full mb-2 p-2 border rounded"
          type="text"
          placeholder="Cover image path (e.g. /covers/newbook.jpg)"
          value={cover}
          required
          onChange={e => setCover(e.target.value)}
        />
        <textarea
          className="w-full mb-2 p-2 border rounded"
          placeholder="Description"
          value={description}
          required
          onChange={e => setDescription(e.target.value)}
        />
        <button className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-700" type="submit">
          Add Book
        </button>
      </form>
      <h3 className="text-lg font-semibold mb-4">Books Added</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {adminBooks.length === 0 ? (
          <p>No books added yet.</p>
        ) : (
          adminBooks.map(book => (
            <div key={book.id} className="bg-white rounded shadow p-4 flex flex-col">
              <img src={book.cover} alt={book.title} className="h-48 w-full object-cover rounded mb-4" />
              <h4 className="font-bold">{book.title}</h4>
              <p className="text-gray-600">{book.author}</p>
              <p className="text-indigo-600 font-semibold mt-2">${book.price.toFixed(2)}</p>
              <p className="text-sm mt-2">{book.description}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

// Main App
function BookStoreApp() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<BookStore />} />
        <Route path="/book/:id" element={<BookDetails />} />
        <Route path="/wishlist" element={<Wishlist />} />
        <Route path="/admin" element={<Admin />} />
      </Routes>
    </Router>
  );
}

export default BookStoreApp;
