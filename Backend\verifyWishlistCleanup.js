const mongoose = require('mongoose');
require('./db/config');

async function verifyWishlistCleanup() {
  let issuesFound = 0;

  try {
    console.log('🔍 Verifying wishlist cleanup...');

    // Get database connection
    const db = mongoose.connection.db;
    
    // 1. Check for wishlist collections
    console.log('\n1️⃣ Checking for wishlist collections...');
    const collections = await db.listCollections().toArray();
    const wishlistCollections = collections.filter(collection => 
      collection.name.toLowerCase().includes('wishlist')
    );
    
    if (wishlistCollections.length > 0) {
      console.log(`❌ Found ${wishlistCollections.length} wishlist collection(s):`);
      wishlistCollections.forEach(collection => {
        console.log(`  - ${collection.name}`);
      });
      issuesFound += wishlistCollections.length;
    } else {
      console.log('✅ No wishlist collections found');
    }
    
    // 2. Check users collection for wishlist fields
    console.log('\n2️⃣ Checking users collection for wishlist fields...');
    try {
      const usersCollection = db.collection('users');
      const userCount = await usersCollection.countDocuments();
      
      if (userCount > 0) {
        const usersWithWishlist = await usersCollection.find({
          $or: [
            { wishlist: { $exists: true } },
            { wishlistItems: { $exists: true } },
            { wishlistCount: { $exists: true } }
          ]
        }).toArray();
        
        if (usersWithWishlist.length > 0) {
          console.log(`❌ Found ${usersWithWishlist.length} user(s) with wishlist fields:`);
          usersWithWishlist.forEach(user => {
            const wishlistFields = [];
            if (user.wishlist !== undefined) wishlistFields.push('wishlist');
            if (user.wishlistItems !== undefined) wishlistFields.push('wishlistItems');
            if (user.wishlistCount !== undefined) wishlistFields.push('wishlistCount');
            console.log(`  - User ${user._id}: ${wishlistFields.join(', ')}`);
          });
          issuesFound += usersWithWishlist.length;
        } else {
          console.log('✅ No wishlist fields found in users collection');
        }
      } else {
        console.log('ℹ️  Users collection is empty');
      }
    } catch (error) {
      console.log('ℹ️  Users collection does not exist');
    }
    
    // 3. Check items/books collection for wishlist fields
    console.log('\n3️⃣ Checking items collection for wishlist fields...');
    try {
      const itemsCollection = db.collection('additems');
      const itemCount = await itemsCollection.countDocuments();
      
      if (itemCount > 0) {
        const itemsWithWishlist = await itemsCollection.find({
          $or: [
            { wishlistCount: { $exists: true } },
            { inWishlist: { $exists: true } },
            { wishlistedBy: { $exists: true } }
          ]
        }).toArray();
        
        if (itemsWithWishlist.length > 0) {
          console.log(`❌ Found ${itemsWithWishlist.length} item(s) with wishlist fields:`);
          itemsWithWishlist.forEach(item => {
            const wishlistFields = [];
            if (item.wishlistCount !== undefined) wishlistFields.push('wishlistCount');
            if (item.inWishlist !== undefined) wishlistFields.push('inWishlist');
            if (item.wishlistedBy !== undefined) wishlistFields.push('wishlistedBy');
            console.log(`  - Item ${item._id} (${item.title}): ${wishlistFields.join(', ')}`);
          });
          issuesFound += itemsWithWishlist.length;
        } else {
          console.log('✅ No wishlist fields found in items collection');
        }
      } else {
        console.log('ℹ️  Items collection is empty');
      }
    } catch (error) {
      console.log('ℹ️  Items collection does not exist');
    }
    
    // 4. Check for any other collections that might contain wishlist data
    console.log('\n4️⃣ Checking other collections for wishlist references...');
    const otherCollections = collections.filter(collection => 
      !collection.name.toLowerCase().includes('wishlist') &&
      !['users', 'additems', 'admins', 'sellers', 'myorders'].includes(collection.name.toLowerCase())
    );
    
    for (const collection of otherCollections) {
      try {
        const coll = db.collection(collection.name);
        const count = await coll.countDocuments();
        
        if (count > 0) {
          // Sample a few documents to check for wishlist references
          const sampleDocs = await coll.find().limit(5).toArray();
          const docsWithWishlist = sampleDocs.filter(doc => 
            JSON.stringify(doc).toLowerCase().includes('wishlist')
          );
          
          if (docsWithWishlist.length > 0) {
            console.log(`⚠️  Collection '${collection.name}' may contain wishlist references`);
            console.log(`   Sample documents with wishlist references: ${docsWithWishlist.length}/${sampleDocs.length}`);
            issuesFound++;
          }
        }
      } catch (error) {
        // Skip collections that can't be accessed
      }
    }
    
    // 5. Summary
    console.log('\n📋 Verification Summary:');
    console.log('='.repeat(50));
    
    if (issuesFound === 0) {
      console.log('🎉 SUCCESS: Database is completely clean of wishlist data!');
      console.log('✅ No wishlist collections found');
      console.log('✅ No wishlist fields in user documents');
      console.log('✅ No wishlist fields in item documents');
      console.log('✅ No wishlist references in other collections');
    } else {
      console.log(`❌ ISSUES FOUND: ${issuesFound} wishlist-related issue(s) detected`);
      console.log('🔧 Please run the cleanup script again or manually remove the remaining data');
    }
    
    console.log('\n📊 Database Collections:');
    collections.forEach(collection => {
      console.log(`  - ${collection.name}`);
    });
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(issuesFound > 0 ? 1 : 0);
  }
}

// Run the verification
console.log('🚀 Starting MongoDB Wishlist Cleanup Verification...');

// Wait for database connection
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  verifyWishlistCleanup();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
