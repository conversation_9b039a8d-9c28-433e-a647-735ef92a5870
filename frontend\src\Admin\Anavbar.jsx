// Ultra Modern Admin Navigation Component

import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { FaBook, FaHome, FaUsers, FaStore, FaUserShield, FaSignOutAlt, FaBars, FaTimes } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Anavbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name || JSON.parse(user).email?.split('@')[0] || 'Admin';
    } catch (e) {
      userName = 'Admin';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/');
  };

  const isActive = (path) => location.pathname === path;

  const navLinks = [
    { path: '/admin/dashboard', label: 'Dashboard', icon: FaHome },
    { path: '/admin/books', label: 'Books', icon: FaBook },
    { path: '/admin/users', label: 'Users', icon: FaUsers },
    { path: '/admin/sellers', label: 'Sellers', icon: FaStore },
  ];

  return (
    <nav className="modern-nav sticky top-0 z-50" style={{ background: 'rgba(239, 68, 68, 0.2)' }}>
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/admin/dashboard" className="flex items-center space-x-3 group">
            <div className="p-2 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl group-hover:scale-110 transition-transform">
              <FaUserShield className="text-white text-xl" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
                BookStore
              </span>
              <span className="text-xs text-gray-400">Admin Portal</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navLinks.map(({ path, label, icon: Icon }) => (
              <Link
                key={path}
                to={path}
                className={`nav-link ${isActive(path) ? 'active bg-red-500/20 text-red-300' : ''}`}
              >
                <Icon className="text-sm" />
                <span>{label}</span>
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-white">
              <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                <FaUserShield className="text-sm" />
              </div>
              <span className="font-medium">{userName}</span>
            </div>
            <button
              onClick={handleLogout}
              className="btn-modern flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2"
            >
              <FaSignOutAlt className="text-sm" />
              <span>Logout</span>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
          >
            {isMenuOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-black/90 backdrop-blur-xl border-t border-white/10">
            <div className="px-6 py-4 space-y-2">
              {navLinks.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center space-x-3 p-3 rounded-xl transition-colors ${
                    isActive(path)
                      ? 'bg-red-500/20 text-red-300'
                      : 'text-white hover:bg-white/10'
                  }`}
                >
                  <Icon />
                  <span>{label}</span>
                </Link>
              ))}

              <div className="border-t border-white/10 pt-4 mt-4">
                <div className="flex items-center space-x-3 p-3 text-white">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                    <FaUserShield className="text-sm" />
                  </div>
                  <span>{userName}</span>
                </div>
                <button
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center space-x-3 p-3 text-red-400 hover:bg-red-500/10 rounded-xl transition-colors w-full"
                >
                  <FaSignOutAlt />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Anavbar;
