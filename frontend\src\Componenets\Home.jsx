// src/components/Home.js - Ultra Modern BookStore Landing Page

import React, { useState, useEffect } from 'react';
import { Link } from "react-router-dom";
import { FaBook, FaUsers, FaStore, FaUserShield, FaStar, FaHeart, FaShoppingCart, FaArrowRight, FaQuoteLeft } from 'react-icons/fa';
import './Home.css';
import '../styles/ModernDesignSystem.css';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % 3);
    }, 4000);

    // Create floating particles
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 8 + 's';
      particle.style.animationDuration = (Math.random() * 3 + 5) + 's';
      document.body.appendChild(particle);

      setTimeout(() => {
        if (document.body.contains(particle)) {
          document.body.removeChild(particle);
        }
      }, 8000);
    };

    const particleInterval = setInterval(createParticle, 2000);

    return () => {
      clearInterval(interval);
      clearInterval(particleInterval);
    };
  }, []);

  const testimonials = [
    { name: "Sarah Johnson", text: "Amazing collection of books! Found exactly what I was looking for.", rating: 5 },
    { name: "Mike Chen", text: "Great platform for selling my old textbooks. Highly recommended!", rating: 5 },
    { name: "Emma Davis", text: "User-friendly interface and fast delivery. Love this bookstore!", rating: 5 }
  ];

  const stats = [
    { number: "10,000+", label: "Happy Readers" },
    { number: "5,000+", label: "Books Available" },
    { number: "1,000+", label: "Trusted Sellers" },
    { number: "99%", label: "Customer Satisfaction" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-2000"></div>
      </div>
      {/* Ultra Modern Navigation */}
      <nav className="bg-black/20 backdrop-blur-xl border-b border-white/10 sticky top-0 z-50 relative">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-3 text-3xl font-bold text-white hover:text-purple-300 transition-all duration-300 transform hover:scale-105">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
                <FaBook className="text-white text-xl" />
              </div>
              <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">BookStore</span>
            </Link>
            <div className="hidden md:flex items-center space-x-4">
              <Link to="/login" className="group flex items-center space-x-2 px-6 py-3 text-white hover:text-purple-300 hover:bg-white/10 rounded-xl transition-all duration-300 border border-transparent hover:border-purple-400/30">
                <FaUsers className="group-hover:scale-110 transition-transform" />
                <span className="font-medium">Customer</span>
              </Link>
              <Link to="/slogin" className="group flex items-center space-x-2 px-6 py-3 text-white hover:text-green-300 hover:bg-white/10 rounded-xl transition-all duration-300 border border-transparent hover:border-green-400/30">
                <FaStore className="group-hover:scale-110 transition-transform" />
                <span className="font-medium">Seller</span>
              </Link>
              <Link to="/alogin" className="group flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25">
                <FaUserShield className="group-hover:scale-110 transition-transform" />
                <span className="font-medium">Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="container mx-auto px-6 py-20 relative z-10">
        <div className={`text-center max-w-6xl mx-auto transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="mb-8">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 rounded-full text-purple-300 text-sm font-medium backdrop-blur-sm">
              ✨ Your Ultimate Book Destination
            </span>
          </div>

          <h1 className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight float-animation">
            Welcome to{' '}
            <span className="gradient-text-animated">
              BookStore
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-4xl mx-auto">
            Discover thousands of books, connect with passionate readers, and build your literary journey in our
            <span className="text-purple-400 font-semibold"> modern digital marketplace</span>
          </p>

          {/* Stats Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className={`text-center transition-all duration-1000 delay-${index * 200}`}>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-gray-400 text-sm md:text-base">{stat.label}</div>
              </div>
            ))}
          </div>

          {/* Action Cards */}
          <div className="grid md:grid-cols-3 gap-8 mt-16">
            <Link to="/login" className="group">
              <div className="glass-morphism card-hover-effect p-8 rounded-3xl border border-white/20 hover:border-purple-400/50 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/25 relative overflow-hidden neon-glow">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
                    <FaUsers className="text-white text-3xl" />
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors">Browse & Buy</h3>
                  <p className="text-gray-300 leading-relaxed mb-6">
                    Explore our vast collection of books, add favorites to your cart, and enjoy seamless purchasing.
                  </p>
                  <div className="flex items-center text-purple-400 font-medium group-hover:text-purple-300">
                    <span>Start Reading</span>
                    <FaArrowRight className="ml-2 group-hover:translate-x-2 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>

            <Link to="/slogin" className="group">
              <div className="bg-white/10 backdrop-blur-xl p-8 rounded-3xl border border-white/20 hover:border-green-400/50 transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl hover:shadow-green-500/25 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
                    <FaStore className="text-white text-3xl" />
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors">Sell Books</h3>
                  <p className="text-gray-300 leading-relaxed mb-6">
                    List your books, manage inventory, and grow your business with our powerful seller tools.
                  </p>
                  <div className="flex items-center text-green-400 font-medium group-hover:text-green-300">
                    <span>Start Selling</span>
                    <FaArrowRight className="ml-2 group-hover:translate-x-2 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>

            <Link to="/alogin" className="group">
              <div className="bg-white/10 backdrop-blur-xl p-8 rounded-3xl border border-white/20 hover:border-orange-400/50 transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl hover:shadow-orange-500/25 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg">
                    <FaUserShield className="text-white text-3xl" />
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 group-hover:text-orange-300 transition-colors">Admin Panel</h3>
                  <p className="text-gray-300 leading-relaxed mb-6">
                    Manage users, oversee operations, and maintain the marketplace with advanced admin tools.
                  </p>
                  <div className="flex items-center text-orange-400 font-medium group-hover:text-orange-300">
                    <span>Admin Access</span>
                    <FaArrowRight className="ml-2 group-hover:translate-x-2 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* Testimonials Section */}
          <div className="mt-24">
            <h2 className="text-4xl font-bold text-white text-center mb-12">
              What Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">Readers Say</span>
            </h2>
            <div className="max-w-4xl mx-auto">
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5"></div>
                <div className="relative z-10 text-center">
                  <FaQuoteLeft className="text-4xl text-purple-400 mx-auto mb-6" />
                  <p className="text-xl text-gray-300 mb-6 leading-relaxed">
                    "{testimonials[currentTestimonial].text}"
                  </p>
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <FaStar key={i} className="text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-purple-300 font-semibold">
                    - {testimonials[currentTestimonial].name}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-20 text-center">
            <Link to="/login" className="inline-flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25">
              <FaBook />
              <span>Start Your Journey</span>
              <FaArrowRight />
            </Link>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Menu */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-black/80 backdrop-blur-xl border-t border-white/20 p-4 z-50">
        <div className="flex justify-around">
          <Link to="/login" className="flex flex-col items-center space-y-2 text-purple-400 hover:text-purple-300 transition-colors">
            <div className="p-2 bg-purple-500/20 rounded-xl">
              <FaUsers className="text-lg" />
            </div>
            <span className="text-xs font-medium">Customer</span>
          </Link>
          <Link to="/slogin" className="flex flex-col items-center space-y-2 text-green-400 hover:text-green-300 transition-colors">
            <div className="p-2 bg-green-500/20 rounded-xl">
              <FaStore className="text-lg" />
            </div>
            <span className="text-xs font-medium">Seller</span>
          </Link>
          <Link to="/alogin" className="flex flex-col items-center space-y-2 text-orange-400 hover:text-orange-300 transition-colors">
            <div className="p-2 bg-orange-500/20 rounded-xl">
              <FaUserShield className="text-lg" />
            </div>
            <span className="text-xs font-medium">Admin</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
