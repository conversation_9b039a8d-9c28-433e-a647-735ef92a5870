const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

async function scanAndUpdateImages() {
  try {
    console.log('🔍 Scanning current uploads directory...');
    
    // Check uploads directory
    const uploadsPath = path.join(__dirname, 'uploads');
    
    if (!fs.existsSync(uploadsPath)) {
      console.log('❌ Uploads directory does not exist');
      return;
    }
    
    const files = fs.readdirSync(uploadsPath);
    const imageFiles = files.filter(file => 
      file.toLowerCase().endsWith('.jpg') || 
      file.toLowerCase().endsWith('.jpeg') || 
      file.toLowerCase().endsWith('.png') ||
      file.toLowerCase().endsWith('.webp')
    );
    
    console.log(`📁 Found ${imageFiles.length} image files in uploads:`);
    imageFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file}`);
    });
    
    // Get all books from database
    const books = await items.find();
    console.log(`\n📚 Found ${books.length} books in database`);
    
    console.log('\n🔧 Updating database with current image files...');
    
    let updatedCount = 0;
    
    for (const book of books) {
      console.log(`\n📖 Processing: ${book.title}`);
      console.log(`   🔍 Current image: ${book.itemImage}`);
      
      // Try to find a matching image file
      let matchingFile = null;
      
      // Method 1: Exact filename match (if current path exists)
      if (imageFiles.includes(book.itemImage)) {
        matchingFile = book.itemImage;
        console.log(`   ✅ Exact match found: ${matchingFile}`);
      }
      // Method 2: Look for files containing book title words
      else {
        const titleWords = book.title.toLowerCase().split(' ');
        
        matchingFile = imageFiles.find(file => {
          const fileName = file.toLowerCase();
          // Check if filename contains any significant words from the title
          return titleWords.some(word => {
            if (word.length > 3) { // Only check words longer than 3 characters
              return fileName.includes(word);
            }
            return false;
          });
        });
        
        if (matchingFile) {
          console.log(`   ✅ Title-based match found: ${matchingFile}`);
        }
      }
      
      // Method 3: Manual mapping for common books
      if (!matchingFile) {
        const manualMapping = {
          "The Great Gatsby": imageFiles.find(f => f.toLowerCase().includes('gatsby')),
          "To Kill a Mockingbird": imageFiles.find(f => f.toLowerCase().includes('mockingbird')),
          "1984": imageFiles.find(f => f.toLowerCase().includes('1984') || f.toLowerCase().includes('orwell')),
          "Pride and Prejudice": imageFiles.find(f => f.toLowerCase().includes('pride') || f.toLowerCase().includes('prejudice')),
          "The Catcher in the Rye": imageFiles.find(f => f.toLowerCase().includes('catcher')),
          "Harry Potter and the Philosopher's Stone": imageFiles.find(f => f.toLowerCase().includes('harry') || f.toLowerCase().includes('potter')),
          "The Lord of the Rings": imageFiles.find(f => f.toLowerCase().includes('lord') || f.toLowerCase().includes('rings')),
          "Dune": imageFiles.find(f => f.toLowerCase().includes('dune'))
        };
        
        matchingFile = manualMapping[book.title];
        if (matchingFile) {
          console.log(`   ✅ Manual mapping found: ${matchingFile}`);
        }
      }
      
      // Method 4: If still no match, assign the first available image or use a default pattern
      if (!matchingFile && imageFiles.length > 0) {
        // Try to assign based on index
        const bookIndex = books.findIndex(b => b._id.toString() === book._id.toString());
        if (bookIndex < imageFiles.length) {
          matchingFile = imageFiles[bookIndex];
          console.log(`   ⚠️  No specific match, using index-based assignment: ${matchingFile}`);
        }
      }
      
      if (matchingFile) {
        // Update the book in database
        await items.findByIdAndUpdate(book._id, {
          itemImage: matchingFile
        });
        
        updatedCount++;
        console.log(`   💾 Updated database with: ${matchingFile}`);
      } else {
        console.log(`   ❌ No matching image found for: ${book.title}`);
      }
    }
    
    console.log(`\n🎉 Update complete!`);
    console.log(`📊 Updated ${updatedCount} out of ${books.length} books`);
    
    // Show final results
    console.log('\n📋 Final Book-Image Mapping:');
    const updatedBooks = await items.find();
    
    updatedBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   🖼️  Image: ${book.itemImage}`);
      console.log(`   🔗 URL: http://localhost:4000/uploads/${book.itemImage}`);
      
      // Check if file actually exists
      const filePath = path.join(uploadsPath, book.itemImage);
      const exists = fs.existsSync(filePath);
      console.log(`   ${exists ? '✅' : '❌'} File exists: ${exists}`);
    });
    
    console.log('\n🌐 Image URLs are now updated!');
    console.log('🔄 Please refresh your browser to see the updated images');

  } catch (error) {
    console.error('❌ Error scanning and updating images:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Image Scan and Update...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  scanAndUpdateImages();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
