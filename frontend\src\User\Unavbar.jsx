// Ultra Modern User Navigation Component

import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from "react-router-dom";
import { FaBook, FaHome, FaShoppingCart, FaUser, FaSignOutAlt, FaEye, FaBars, FaTimes } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Unavbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name || JSON.parse(user).email?.split('@')[0];
    } catch (e) {
      userName = 'User';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('cart');
    navigate('/wishes');
  };

  const isActive = (path) => location.pathname === path;

  const navLinks = [
    { path: '/uhome', label: 'Home', icon: FaHome },
    { path: '/books', label: 'Books', icon: FaBook },
    { path: '/mycart', label: 'My Cart', icon: FaShoppingCart },
    { path: '/myorders-new', label: 'My Orders', icon: FaEye },
  ];

  return (
    <nav className="modern-nav sticky top-0 z-50">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/uhome" className="flex items-center space-x-3 group">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl group-hover:scale-110 transition-transform">
              <FaBook className="text-white text-xl" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              BookStore
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navLinks.map(({ path, label, icon: Icon }) => (
              <Link
                key={path}
                to={path}
                className={`nav-link ${isActive(path) ? 'active' : ''}`}
              >
                <Icon className="text-sm" />
                <span>{label}</span>
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-white">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <FaUser className="text-sm" />
              </div>
              <span className="font-medium">{userName}</span>
            </div>
            <button
              onClick={handleLogout}
              className="btn-modern flex items-center space-x-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2"
            >
              <FaSignOutAlt className="text-sm" />
              <span>Logout</span>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-white hover:bg-white/10 rounded-lg transition-colors"
          >
            {isMenuOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-black/90 backdrop-blur-xl border-t border-white/10">
            <div className="px-6 py-4 space-y-2">
              {navLinks.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  onClick={() => setIsMenuOpen(false)}
                  className={`flex items-center space-x-3 p-3 rounded-xl transition-colors ${
                    isActive(path)
                      ? 'bg-purple-500/20 text-purple-300'
                      : 'text-white hover:bg-white/10'
                  }`}
                >
                  <Icon />
                  <span>{label}</span>
                </Link>
              ))}

              <div className="border-t border-white/10 pt-4 mt-4">
                <div className="flex items-center space-x-3 p-3 text-white">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                    <FaUser className="text-sm" />
                  </div>
                  <span>{userName}</span>
                </div>
                <button
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center space-x-3 p-3 text-red-400 hover:bg-red-500/10 rounded-xl transition-colors w-full"
                >
                  <FaSignOutAlt />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Unavbar;
