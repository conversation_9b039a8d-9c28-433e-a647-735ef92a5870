import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import { FaBook, FaShoppingCart, FaStar, FaSearch, FaFilter, FaHeart, FaEye } from 'react-icons/fa';
import Unavbar from './Unavbar';
import '../styles/ModernDesignSystem.css';

const Products = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchBooks();
    setTimeout(() => setIsVisible(true), 100);
  }, []);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:4000/books');
      setBooks(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching books:', err);
      setError('Failed to load books. Please try again later.');
      // Fallback to empty array if API fails
      setBooks([]);
    } finally {
      setLoading(false);
    }
  };



  const addToCart = (book) => {
    try {
      // Get existing cart from localStorage
      const existingCart = JSON.parse(localStorage.getItem('cart') || '[]');

      // Check if book already exists in cart
      const existingItemIndex = existingCart.findIndex(item => item._id === book._id);

      if (existingItemIndex > -1) {
        // If book exists, increase quantity
        existingCart[existingItemIndex].quantity += 1;
      } else {
        // If book doesn't exist, add new item
        existingCart.push({
          ...book,
          quantity: 1,
          addedAt: new Date().toISOString()
        });
      }

      // Save updated cart to localStorage
      localStorage.setItem('cart', JSON.stringify(existingCart));

      // Show success message with modern notification
      // You could implement a toast notification here instead of alert
      alert(`"${book.title}" has been added to your cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart. Please try again.');
    }
  };

  // Filter books based on search and genre
  const filteredBooks = books.filter(book => {
    const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         book.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGenre = selectedGenre === '' || book.genre === selectedGenre;
    return matchesSearch && matchesGenre;
  });

  // Get unique genres for filter
  const genres = [...new Set(books.map(book => book.genre).filter(Boolean))];

  if (loading) {
    return (
      <div className="min-h-screen bg-modern-dark flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading our amazing book collection...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-modern-dark">
      <Unavbar />

      <div className="container mx-auto px-6 py-8 relative z-10">
        {/* Header Section */}
        <div className={`text-center mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <h1 className="text-5xl font-bold text-white mb-4">
            Discover Amazing <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">Books</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Explore our curated collection of {books.length} incredible books across all genres
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className={`mb-8 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="glass-card p-6 mb-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search books or authors..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="form-input pl-12 w-full"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <select
                  value={selectedGenre}
                  onChange={(e) => setSelectedGenre(e.target.value)}
                  className="form-input min-w-[150px]"
                >
                  <option value="">All Genres</option>
                  {genres.map(genre => (
                    <option key={genre} value={genre}>{genre}</option>
                  ))}
                </select>

                <button
                  onClick={() => navigate('/mycart')}
                  className="btn-modern btn-success flex items-center space-x-2"
                >
                  <FaShoppingCart />
                  <span>My Cart</span>
                </button>

                <button
                  onClick={() => navigate('/myorders-new')}
                  className="btn-modern btn-secondary flex items-center space-x-2"
                >
                  <FaEye />
                  <span>My Orders</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-4 text-red-300 text-center mb-8 backdrop-blur-sm">
            {error}
          </div>
        )}

        {/* Books Grid */}
        <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {filteredBooks.length === 0 ? (
            <div className="text-center py-16">
              <FaBook className="text-6xl text-gray-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">No books found</h3>
              <p className="text-gray-400">Try adjusting your search or filter criteria</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {filteredBooks.map((book, index) => (
                <div
                  key={book._id || book.id}
                  className="modern-card group cursor-pointer"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="relative overflow-hidden rounded-xl mb-4">
                    <img
                      src={getImageUrl(book.itemImage)}
                      alt={book.title}
                      onError={handleImageError}
                      className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="text-xl font-bold text-white group-hover:text-purple-300 transition-colors line-clamp-2">
                      {book.title}
                    </h3>
                    <p className="text-gray-400">by {book.author}</p>
                    <p className="text-gray-300 text-sm line-clamp-3">{book.description}</p>

                    <div className="flex items-center justify-between pt-4">
                      <span className="text-2xl font-bold text-green-400">₹{book.price}</span>
                      <div className="flex items-center space-x-1 text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <FaStar key={i} className="text-sm" />
                        ))}
                      </div>
                    </div>

                    <button
                      onClick={() => addToCart(book)}
                      className="btn-modern btn-primary w-full group/btn"
                    >
                      <FaShoppingCart className="mr-2 group-hover/btn:scale-110 transition-transform" />
                      Add to Cart
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Products;
