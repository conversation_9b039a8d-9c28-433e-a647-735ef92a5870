const mongoose = require('mongoose');
require('./db/config');

async function cleanupWishlistData() {
  try {
    console.log('🧹 Starting wishlist data cleanup...');
    
    // Get database connection
    const db = mongoose.connection.db;
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('📋 Available collections:', collections.map(c => c.name));
    
    // Look for wishlist-related collections
    const wishlistCollections = collections.filter(collection => 
      collection.name.toLowerCase().includes('wishlist')
    );
    
    if (wishlistCollections.length > 0) {
      console.log(`🎯 Found ${wishlistCollections.length} wishlist-related collection(s):`);
      
      for (const collection of wishlistCollections) {
        console.log(`- ${collection.name}`);
        
        // Get document count before deletion
        const count = await db.collection(collection.name).countDocuments();
        console.log(`  📊 Contains ${count} document(s)`);
        
        if (count > 0) {
          // Show sample documents before deletion
          const sampleDocs = await db.collection(collection.name).find().limit(3).toArray();
          console.log('  📄 Sample documents:');
          sampleDocs.forEach((doc, index) => {
            console.log(`    ${index + 1}. ${JSON.stringify(doc, null, 2)}`);
          });
        }
        
        // Drop the collection
        await db.collection(collection.name).drop();
        console.log(`  ✅ Dropped collection: ${collection.name}`);
      }
    } else {
      console.log('✨ No wishlist-related collections found. Database is clean!');
    }
    
    // Also check for any documents in other collections that might reference wishlist
    console.log('\n🔍 Checking other collections for wishlist references...');
    
    // Check users collection for wishlist fields
    const usersCollection = db.collection('users');
    if (await usersCollection.countDocuments() > 0) {
      const usersWithWishlist = await usersCollection.find({
        $or: [
          { wishlist: { $exists: true } },
          { wishlistItems: { $exists: true } }
        ]
      }).toArray();
      
      if (usersWithWishlist.length > 0) {
        console.log(`👥 Found ${usersWithWishlist.length} user(s) with wishlist fields`);
        
        // Remove wishlist fields from users
        const updateResult = await usersCollection.updateMany(
          {},
          { 
            $unset: { 
              wishlist: "",
              wishlistItems: "",
              wishlistCount: ""
            }
          }
        );
        
        console.log(`  ✅ Cleaned wishlist fields from ${updateResult.modifiedCount} user document(s)`);
      } else {
        console.log('👥 No wishlist fields found in users collection');
      }
    }
    
    // Check items/books collection for wishlist-related fields
    const itemsCollection = db.collection('additems'); // This might be the books collection
    if (await itemsCollection.countDocuments() > 0) {
      const itemsWithWishlist = await itemsCollection.find({
        $or: [
          { wishlistCount: { $exists: true } },
          { inWishlist: { $exists: true } },
          { wishlistedBy: { $exists: true } }
        ]
      }).toArray();
      
      if (itemsWithWishlist.length > 0) {
        console.log(`📚 Found ${itemsWithWishlist.length} item(s) with wishlist fields`);
        
        // Remove wishlist fields from items
        const updateResult = await itemsCollection.updateMany(
          {},
          { 
            $unset: { 
              wishlistCount: "",
              inWishlist: "",
              wishlistedBy: ""
            }
          }
        );
        
        console.log(`  ✅ Cleaned wishlist fields from ${updateResult.modifiedCount} item document(s)`);
      } else {
        console.log('📚 No wishlist fields found in items collection');
      }
    }
    
    console.log('\n🎉 Wishlist cleanup completed successfully!');
    console.log('📋 Summary:');
    console.log(`  - Removed ${wishlistCollections.length} wishlist collection(s)`);
    console.log('  - Cleaned wishlist fields from user and item documents');
    console.log('  - Database is now free of wishlist-related data');
    
  } catch (error) {
    console.error('❌ Error during wishlist cleanup:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the cleanup
console.log('🚀 Starting MongoDB Wishlist Cleanup Script...');
console.log('⚠️  This will permanently remove all wishlist-related data from the database');

// Wait for database connection
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  cleanupWishlistData();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
