const mongoose = require('mongoose');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

// Correct mapping of books to their proper images
const correctMapping = {
  "The Great Gatsby": "The Great Gatsby.jpeg",
  "To Kill a Mockingbird": "To Kill a Mockingbird.jpeg", 
  "1984": "1984 by <PERSON>.jpe<PERSON>",
  "Pride and Prejudice": "The Fault in Our Stars.jpeg", // Using available image
  "The Catcher in the Rye": "The Catcher in the Rye.jpeg",
  "<PERSON> Potter and the Philosopher's Stone": "The Hunger Games.jpeg", // Using available image
  "The Lord of the Rings": "The Kite Runner.jpeg", // Using available image
  "Dune": "The Da Vinci Code.jpeg" // Using available image
};

async function fixBookImageMapping() {
  try {
    console.log('🔧 Fixing book-image mapping...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Found ${books.length} books to fix`);
    
    let updatedCount = 0;
    
    for (const book of books) {
      const correctImage = correctMapping[book.title];
      
      if (correctImage) {
        console.log(`📖 ${book.title}`);
        console.log(`   🔄 Updating: ${book.itemImage} → ${correctImage}`);
        
        await items.findByIdAndUpdate(book._id, {
          itemImage: correctImage
        });
        
        updatedCount++;
      }
    }
    
    console.log(`\n✅ Fixed ${updatedCount} book-image mappings`);
    
    // Show final results
    console.log('\n📋 Corrected Book-Image Mapping:');
    const updatedBooks = await items.find();
    
    updatedBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   🖼️  Image: ${book.itemImage}`);
      console.log(`   🔗 URL: http://localhost:4000/uploads/${book.itemImage}`);
    });

  } catch (error) {
    console.error('❌ Error fixing mapping:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Book-Image Mapping Fix...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  fixBookImageMapping();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
