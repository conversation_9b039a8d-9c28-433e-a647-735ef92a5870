import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { <PERSON>aB<PERSON>, <PERSON>a<PERSON>ser, FaLock, FaEye, FaEyeSlash, FaArrowRight, FaHome, FaStore } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

// Ultra Modern BookStore Seller Login Page
const SellerLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    setTimeout(() => setIsVisible(true), 100);
  }, []);

  axios.defaults.withCredentials = true;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const payload = { email, password };
      const res = await axios.post("http://localhost:4000/slogin", payload);
      if (res.data.Status === "Success") {
        localStorage.setItem('user', JSON.stringify(res.data.user));
        navigate('/seller/dashboard');
      } else {
        alert("Wrong credentials");
      }
    } catch (err) {
      alert("Error logging in. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/seller/signup");
  };

  return (
    <div className="min-h-screen bg-modern-dark flex items-center justify-center px-6 py-12">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      {/* Navigation */}
      <div className="absolute top-6 left-6 z-50">
        <button
          onClick={() => navigate('/')}
          className="btn-modern btn-secondary flex items-center space-x-2"
        >
          <FaHome />
          <span>Back to Home</span>
        </button>
      </div>

      <div className={`w-full max-w-lg relative z-10 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
        <div className="glass-card-strong p-8 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>

          <div className="relative z-10">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mb-4">
                <FaStore className="text-white text-2xl" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-2">Seller Portal</h2>
              <p className="text-gray-300">Sign in to manage your bookstore</p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaUser className="text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="form-input pl-12"
                    placeholder="Enter your seller email"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FaLock className="text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="form-input pl-12 pr-12"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="btn-modern btn-success w-full text-lg py-4 shadow-2xl hover:shadow-green-500/25 group"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Signing In...
                  </>
                ) : (
                  <>
                    <FaStore className="mr-2 group-hover:scale-110 transition-transform" />
                    <span>Access Seller Portal</span>
                    <FaArrowRight className="ml-2 group-hover:translate-x-2 transition-transform" />
                  </>
                )}
              </button>
            </form>

            <div className="mt-8 text-center">
              <span className="text-gray-300">Don't have a seller account?</span>
              <button
                onClick={handleSignup}
                className="ml-2 text-green-400 hover:text-green-300 font-semibold transition-colors duration-300 hover:underline"
              >
                Register as Seller
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerLogin;
