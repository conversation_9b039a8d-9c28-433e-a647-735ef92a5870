const axios = require('axios');

async function testDemoLogin() {
  try {
    console.log('🧪 Testing demo user login...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'demo123'
    };

    console.log('📤 Sending login request...');
    const response = await axios.post('http://localhost:4000/login', loginData);
    
    console.log('📥 Response received:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log('✅ Login test SUCCESSFUL!');
      console.log(`👤 User: ${response.data.user.name}`);
      console.log(`📧 Email: ${response.data.user.email}`);
      console.log(`🆔 ID: ${response.data.user.id}`);
    } else {
      console.log('❌ Login test FAILED!');
      console.log(`📝 Message: ${response.data.message}`);
    }

  } catch (error) {
    console.error('❌ Error testing login:', error.message);
    
    if (error.response) {
      console.log('📥 Error response:', error.response.data);
    } else if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Backend server is not running on http://localhost:4000');
      console.log('💡 Please start the backend server first');
    }
  }
}

// Run the test
console.log('🚀 Starting Demo Login Test...');
testDemoLogin();
