import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const navigate = useNavigate();

  // Check backend connection on component mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const response = await axios.get('http://localhost:4000/books');
        setConnectionStatus('connected');
      } catch (err) {
        setConnectionStatus('disconnected');
        console.error('Backend connection failed:', err);
      }
    };
    checkConnection();
  }, []);

  axios.defaults.withCredentials = true;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const payload = { email: email.trim(), password };
      console.log('Attempting login with:', { email: email.trim(), password: '***' });

      // Try the main login endpoint first
      let response;
      try {
        response = await axios.post("http://localhost:4000/login", payload);
      } catch (firstError) {
        console.log('Main login endpoint failed, trying API endpoint...');
        response = await axios.post("http://localhost:4000/api/login", payload);
      }

      console.log('Login response:', response.data);

      if (response.data.success) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
        console.log('User stored in localStorage:', response.data.user);
        alert('Login successful! Redirecting to welcome page...');
        navigate('/welcome');
      } else {
        setError(response.data.message || 'Invalid credentials');
      }
    } catch (err) {
      console.error('Login error:', err);
      if (err.response) {
        console.error('Error response:', err.response.data);
        setError(err.response.data.message || 'Login failed. Please check your credentials.');
      } else if (err.request) {
        setError('Cannot connect to server. Please check if the backend is running.');
      } else {
        setError('Login failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/signup");
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-teal-100 to-white">
      <header className="w-full py-6 bg-teal-600 shadow-md mb-8">
        <div className="container mx-auto flex items-center justify-between px-4">
          <h1 className="text-2xl font-bold text-white cursor-pointer" onClick={() => navigate('/')}>
            BookStore
          </h1>
          <nav>
            <button
              className="text-white font-semibold hover:underline mr-4"
              onClick={() => navigate('/books')}
            >
              Browse Books
            </button>
            <button
              className="text-white font-semibold hover:underline"
              onClick={handleSignup}
            >
              Signup
            </button>
          </nav>
        </div>
      </header>

      {/* Demo Credentials Banner */}
      <div className="w-full max-w-md bg-gradient-to-r from-green-500 to-teal-600 rounded-lg shadow-xl p-6 mb-6 text-white border-2 border-green-300">
        <div className="text-center">
          <div className="flex items-center justify-center mb-3">
            <span className="text-2xl mr-2">🎯</span>
            <h3 className="text-xl font-bold">Demo Account Ready!</h3>
          </div>
          <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm border border-white/30">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-semibold flex items-center">
                  <span className="mr-2">📧</span>Email:
                </span>
                <span className="font-mono bg-white/40 px-3 py-1 rounded-md text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-semibold flex items-center">
                  <span className="mr-2">🔑</span>Password:
                </span>
                <span className="font-mono bg-white/40 px-3 py-1 rounded-md text-sm">demo123</span>
              </div>
            </div>
          </div>
          <div className="flex gap-2 mt-4">
            <button
              onClick={() => {
                setEmail('<EMAIL>');
                setPassword('demo123');
              }}
              className="flex-1 bg-white/20 hover:bg-white/30 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 border border-white/30 flex items-center justify-center"
            >
              <span className="mr-2">✨</span>Fill Form
            </button>
            <button
              onClick={async () => {
                setEmail('<EMAIL>');
                setPassword('demo123');
                // Auto-submit after a short delay
                setTimeout(() => {
                  document.getElementById('login-form').dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                }, 100);
              }}
              className="flex-1 bg-white text-green-600 hover:bg-green-50 font-semibold py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center"
            >
              <span className="mr-2">🚀</span>Quick Login
            </button>
          </div>
          <p className="text-xs mt-3 text-white/80">
            Click "Quick Login" for instant access or "Fill Form" to see the credentials
          </p>
        </div>
      </div>

      {/* Connection Status */}
      <div className="w-full max-w-md mb-4">
        <div className={`flex items-center justify-center p-2 rounded-lg text-sm ${
          connectionStatus === 'connected'
            ? 'bg-green-100 text-green-800 border border-green-200'
            : connectionStatus === 'disconnected'
            ? 'bg-red-100 text-red-800 border border-red-200'
            : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
        }`}>
          <span className="mr-2">
            {connectionStatus === 'connected' ? '🟢' : connectionStatus === 'disconnected' ? '🔴' : '🟡'}
          </span>
          {connectionStatus === 'connected' && 'Backend Connected'}
          {connectionStatus === 'disconnected' && 'Backend Disconnected - Please start the server'}
          {connectionStatus === 'checking' && 'Checking backend connection...'}
        </div>
      </div>

      <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-center text-teal-700 mb-6">Login to your BookStore account</h2>
        <form id="login-form" className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              placeholder="Email address"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              placeholder="Password"
            />
          </div>
          {error && <div className="text-red-500 text-sm text-center">{error}</div>}
          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-teal-600 hover:bg-teal-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring focus:border-teal-300 transition-all duration-300"
            >
              {loading ? 'Logging in...' : 'Log in'}
            </button>
          </div>
        </form>
        <div className="mt-4 text-center">
          <span className="text-gray-600">Don't have an account?</span>
          <button
            onClick={handleSignup}
            className="ml-2 text-teal-600 hover:underline font-semibold"
          >
            Signup
          </button>
        </div>
      </div>
      <footer className="mt-12 text-gray-500 text-sm">
        &copy; {new Date().getFullYear()} BookStore. All rights reserved.
      </footer>
    </div>
  );
};

export default Login;
