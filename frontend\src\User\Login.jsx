import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { <PERSON>aB<PERSON>, <PERSON>a<PERSON>ser, FaLock, FaEye, FaEyeSlash, FaRocket, FaFill, FaArrowRight, FaHome } from 'react-icons/fa';
import '../styles/ModernDesignSystem.css';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const [showPassword, setShowPassword] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showDemoSuggestion, setShowDemoSuggestion] = useState(false);
  const navigate = useNavigate();

  // Trigger animations on component mount
  useEffect(() => {
    // Trigger entrance animation
    setTimeout(() => setIsVisible(true), 100);

    // Create floating particles
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'fixed w-2 h-2 bg-purple-400 rounded-full opacity-60 pointer-events-none z-0';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = '100%';
      particle.style.animation = `float-up ${Math.random() * 3 + 4}s linear forwards`;
      document.body.appendChild(particle);

      setTimeout(() => {
        if (document.body.contains(particle)) {
          document.body.removeChild(particle);
        }
      }, 7000);
    };

    const particleInterval = setInterval(createParticle, 1500);

    return () => clearInterval(particleInterval);
  }, []);

  axios.defaults.withCredentials = true;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const payload = { email: email.trim(), password };
      console.log('Attempting login with:', { email: email.trim(), password: '***' });

      // Try the main login endpoint first
      let response;
      try {
        response = await axios.post("http://localhost:4000/login", payload);
      } catch (firstError) {
        console.log('Main login endpoint failed, trying API endpoint...');
        response = await axios.post("http://localhost:4000/api/login", payload);
      }

      console.log('Login response:', response.data);

      if (response.data.success) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
        console.log('User stored in localStorage:', response.data.user);
        alert('Login successful! Redirecting to welcome page...');
        navigate('/welcome');
      } else {
        setError(response.data.message || 'Invalid credentials');
      }
    } catch (err) {
      console.error('Login error:', err);
      if (err.response) {
        console.error('Error response:', err.response.data);
        setError(err.response.data.message || 'Login failed. Please check your credentials.');
      } else if (err.request) {
        setError('Cannot connect to server. Please check if the backend is running.');
      } else {
        setError('Login failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/signup");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-2000"></div>
      </div>

      {/* Ultra Modern Navigation */}
      <nav className="bg-black/20 backdrop-blur-xl border-b border-white/10 sticky top-0 z-50 relative">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate('/')}
              className="flex items-center space-x-3 text-2xl font-bold text-white hover:text-purple-300 transition-all duration-300 transform hover:scale-105"
            >
              <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
                <FaBook className="text-white text-lg" />
              </div>
              <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">BookStore</span>
            </button>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/books')}
                className="flex items-center space-x-2 px-4 py-2 text-white hover:text-purple-300 hover:bg-white/10 rounded-xl transition-all duration-300 border border-transparent hover:border-purple-400/30"
              >
                <FaBook className="text-sm" />
                <span>Browse Books</span>
              </button>
              <button
                onClick={handleSignup}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25"
              >
                <FaUser className="text-sm" />
                <span>Signup</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex flex-col items-center justify-center min-h-screen px-6 py-12 relative z-10">





        {/* Ultra Modern Login Form */}
        <div className={`w-full max-w-lg transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 relative overflow-hidden group hover:border-purple-400/50 transition-all duration-500 shadow-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <div className="relative z-10">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-4">
                  <FaUser className="text-white text-2xl" />
                </div>
                <h2 className="text-3xl font-bold text-white mb-2">Welcome Back</h2>
                <p className="text-gray-300">Sign in to your BookStore account</p>
              </div>

              {/* Demo Suggestion Tooltip */}
              {showDemoSuggestion && (
                <div className="mb-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-2xl border border-green-400/30 p-4 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10"></div>
                  <div className="relative z-10 text-center">
                    <div className="flex items-center justify-center mb-3">
                      <span className="text-lg mr-2">💡</span>
                      <span className="text-green-300 font-semibold">Try Demo Account</span>
                    </div>
                    <div className="flex gap-3">
                      <button
                        onClick={() => {
                          setEmail('<EMAIL>');
                          setPassword('demo123');
                          setShowDemoSuggestion(false);
                        }}
                        className="flex-1 bg-green-500/20 hover:bg-green-500/30 text-green-300 font-medium py-2 px-4 rounded-xl transition-all duration-300 border border-green-400/30 flex items-center justify-center text-sm"
                      >
                        <FaUser className="mr-2 text-xs" />
                        Use Demo: <EMAIL>
                      </button>
                      <button
                        onClick={async () => {
                          setEmail('<EMAIL>');
                          setPassword('demo123');
                          setShowDemoSuggestion(false);
                          setTimeout(() => {
                            document.getElementById('login-form').dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                          }, 100);
                        }}
                        className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium py-2 px-4 rounded-xl transition-all duration-300 flex items-center justify-center text-sm"
                      >
                        <FaRocket className="mr-2 text-xs" />
                        Quick Login
                      </button>
                    </div>
                  </div>
                </div>
              )}

              <form id="login-form" className="space-y-6" onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <FaUser className="text-gray-400" />
                      </div>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        onFocus={() => !email && setShowDemoSuggestion(true)}
                        onBlur={() => setTimeout(() => setShowDemoSuggestion(false), 200)}
                        className="w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 transition-all duration-300"
                        placeholder="Enter your email (try <EMAIL>)"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <FaLock className="text-gray-400" />
                      </div>
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        autoComplete="current-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full pl-12 pr-12 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 transition-all duration-300"
                        placeholder="Enter your password (try demo123)"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-white transition-colors"
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-4 text-red-300 text-sm text-center backdrop-blur-sm">
                    {error}
                  </div>
                )}

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-bold py-4 px-6 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 shadow-lg hover:shadow-purple-500/25 flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Signing In...
                    </>
                  ) : (
                    <>
                      <span>Sign In</span>
                      <FaArrowRight className="ml-2" />
                    </>
                  )}
                </button>
              </form>

              <div className="mt-8 text-center">
                <span className="text-gray-300">Don't have an account?</span>
                <button
                  onClick={handleSignup}
                  className="ml-2 text-purple-400 hover:text-purple-300 font-semibold transition-colors duration-300 hover:underline"
                >
                  Create Account
                </button>
              </div>
            </div>
          </div>
        </div>
        {/* Modern Footer */}
        <footer className={`mt-12 text-center transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="flex items-center justify-center space-x-2 text-gray-400 text-sm">
            <span>&copy; {new Date().getFullYear()}</span>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent font-semibold">BookStore</span>
            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
            <span>All rights reserved</span>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Login;
