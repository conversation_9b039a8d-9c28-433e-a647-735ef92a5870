const express = require('express');
const mongoose = require('mongoose');
require('./db/config');

// Import the User model
const User = require('./db/Users/<USER>');

async function testLoginEndpoint() {
  try {
    console.log('🧪 Testing login functionality...');
    
    // Test 1: Check if demo user exists
    console.log('\n1️⃣ Checking demo user in database...');
    const demoUser = await User.findOne({ email: '<EMAIL>' });
    
    if (demoUser) {
      console.log('✅ Demo user found');
      console.log(`   👤 Name: ${demoUser.name}`);
      console.log(`   📧 Email: ${demoUser.email}`);
      console.log(`   🔑 Password: ${demoUser.password}`);
    } else {
      console.log('❌ Demo user not found');
      return;
    }
    
    // Test 2: Simulate login logic
    console.log('\n2️⃣ Testing login logic...');
    const testEmail = '<EMAIL>';
    const testPassword = 'demo123';
    
    const user = await User.findOne({ email: testEmail });
    if (user) {
      if (user.password === testPassword) {
        console.log('✅ Login logic test PASSED');
        console.log('   🎉 Credentials match successfully');
        console.log(`   🆔 User ID: ${user._id}`);
        console.log(`   👤 User Name: ${user.name}`);
      } else {
        console.log('❌ Login logic test FAILED');
        console.log(`   Expected password: ${testPassword}`);
        console.log(`   Actual password: ${user.password}`);
      }
    } else {
      console.log('❌ User not found during login test');
    }
    
    // Test 3: Check server endpoints
    console.log('\n3️⃣ Server endpoint information...');
    console.log('   📍 Backend should be running on: http://localhost:4000');
    console.log('   🔗 Available login endpoints:');
    console.log('      • POST /login');
    console.log('      • POST /api/login');
    console.log('   📱 Frontend should be running on: http://localhost:5174');
    
    console.log('\n✅ All tests completed!');
    console.log('\n📋 Demo Login Instructions:');
    console.log('   1. Go to http://localhost:5174/login');
    console.log('   2. Use the green "Quick Login" button');
    console.log('   3. Or manually enter:');
    console.log('      📧 Email: <EMAIL>');
    console.log('      🔑 Password: demo123');

  } catch (error) {
    console.error('❌ Error during testing:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the test
console.log('🚀 Starting Login Test...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  testLoginEndpoint();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
