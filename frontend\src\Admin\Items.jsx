import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';

// Book Store Navbar
function BookStoreNavbar() {
  return (
    <nav className="bg-purple-800 p-4 text-white flex justify-between items-center">
      <div className="text-2xl font-bold">Book Store</div>
      <div>
        <Link to="/" className="mr-4 text-white hover:underline">Home</Link>
        <Link to="/wishlist" className="text-white hover:underline">Wishlist</Link>
      </div>
    </nav>
  );
}

// Book Card Component
function BookCard({ book }) {
  const navigate = useNavigate();
  // Use cover images from public folder if available
  const coverSrc = book.itemImage && book.itemImage.startsWith('cover')
    ? `/${book.itemImage}`
    : (book.itemImage ? book.itemImage : '/default_cover.jpg');

  return (
    <div className="bg-white p-4 rounded shadow flex flex-col">
      <img
        src={coverSrc}
        alt={book.title}
        className="rounded-t-lg mb-4 object-cover"
        style={{ height: '350px', width: '100%', objectFit: 'cover' }}
        onError={e => { e.target.onerror = null; e.target.src = '/default_cover.jpg'; }}
      />
      <div className="flex-1 flex flex-col">
        <p className="text-xl font-bold mb-1">{book.title}</p>
        <p className="text-gray-700 mb-1">Author: {book.author}</p>
        <p className="text-gray-700 mb-1">Genre: {book.genre}</p>
        <p className="text-blue-500 font-bold mb-3">Price: ${book.price}</p>
        <div className="flex flex-row items-center mt-auto">
          <button
            className="bg-purple-800 text-white px-3 py-1 rounded"
            onClick={() => navigate(`/uitem/${book._id}`)}
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
}

function BookStore() {
  const [books, setBooks] = useState([]);

  // Fetch books on mount
  useEffect(() => {
    // Fetch books from backend
    fetch('/item')
      .then(res => res.json())
      .then(data => setBooks(data))
      .catch(err => console.error('Error fetching books:', err));
  }, []);



  return (
    <div className="min-h-screen bg-gray-100">
      <BookStoreNavbar />
      <div className="container mx-auto p-8">
        <h2 className="text-3xl font-semibold mb-8 text-center">Book Store</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {books.map(book => (
            <BookCard
              key={book._id}
              book={book}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

export default BookStore;
