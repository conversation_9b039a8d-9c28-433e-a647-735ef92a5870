const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('./db/config');

// Import the items model
const items = require('./db/Seller/Additem');

// Mapping of book titles to their correct image filenames
const bookImageMapping = {
  "The Great Gatsby": "The Great Gatsby.jpeg",
  "To Kill a Mockingbird": "To Kill a Mockingbird.jpeg", 
  "1984": "1984 by <PERSON>.jpe<PERSON>",
  "Pride and Prejudice": "Pride and Prejudice.jpeg",
  "The Catcher in the Rye": "The Catcher in the Rye.jpeg",
  "<PERSON> and the Philosopher's Stone": "Harry Potter.jpeg",
  "The Lord of the Rings": "The Lord of the Rings.jpeg",
  "Dune": "Dune.jpeg"
};

async function fixImagePaths() {
  try {
    console.log('🔧 Fixing image paths in database...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Found ${books.length} books to update`);
    
    let updatedCount = 0;
    
    for (const book of books) {
      console.log(`\n📖 Processing: ${book.title}`);
      console.log(`   🔍 Current image: ${book.itemImage}`);
      
      // Check if we have a mapping for this book
      let newImagePath = null;
      
      // First, try exact title match
      if (bookImageMapping[book.title]) {
        newImagePath = bookImageMapping[book.title];
      }
      // Try partial matches for common variations
      else if (book.title.includes("Harry Potter")) {
        newImagePath = "Harry Potter.jpeg";
      }
      else if (book.title.includes("Lord of the Rings")) {
        newImagePath = "The Lord of the Rings.jpeg";
      }
      else {
        // Try to find a matching file in the public directory
        const publicPath = path.join(__dirname, '..', 'frontend', 'public');
        const files = fs.readdirSync(publicPath);
        
        // Look for files that contain the book title
        const matchingFile = files.find(file => {
          const fileName = file.toLowerCase();
          const bookTitle = book.title.toLowerCase();
          return fileName.includes(bookTitle.split(' ')[0]) || 
                 fileName.includes(bookTitle.split(' ')[1]) ||
                 bookTitle.includes(fileName.split('.')[0].toLowerCase());
        });
        
        if (matchingFile) {
          newImagePath = matchingFile;
        }
      }
      
      if (newImagePath) {
        console.log(`   ✅ New image: ${newImagePath}`);
        
        // Update the book in database
        await items.findByIdAndUpdate(book._id, {
          itemImage: newImagePath
        });
        
        updatedCount++;
        console.log(`   💾 Updated in database`);
      } else {
        console.log(`   ⚠️  No matching image found, keeping current path`);
      }
    }
    
    console.log(`\n🎉 Update complete!`);
    console.log(`📊 Updated ${updatedCount} out of ${books.length} books`);
    
    // Verify the updates
    console.log('\n🔍 Verifying updates...');
    const updatedBooks = await items.find();
    
    console.log('\n📋 Updated Book Images:');
    updatedBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.title}`);
      console.log(`   🖼️  Image: ${book.itemImage}`);
      console.log(`   🔗 URL: http://localhost:4000/public/${book.itemImage}`);
    });
    
    console.log('\n✅ All image paths have been updated!');
    console.log('🌐 Images should now load correctly in the frontend');

  } catch (error) {
    console.error('❌ Error fixing image paths:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the function
console.log('🚀 Starting Image Path Fix...');

// Wait for connection to establish
mongoose.connection.once('open', () => {
  console.log('📍 Database: Connected');
  fixImagePaths();
});

mongoose.connection.on('error', (error) => {
  console.error('❌ Database connection error:', error);
  process.exit(1);
});
