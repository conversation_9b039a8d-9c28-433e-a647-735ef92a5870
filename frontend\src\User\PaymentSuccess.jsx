import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import Unavbar from './Unavbar';

const PaymentSuccess = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);

  useEffect(() => {
    // Get order details from navigation state
    if (location.state && location.state.order) {
      setOrder(location.state.order);
    } else {
      // If no order data, redirect to home
      navigate('/uhome');
    }
  }, [location.state, navigate]);



  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!order) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading order details...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Unavbar />
      <div className="container mt-4">
        {/* Success Header */}
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="text-center mb-4">
              <div className="mb-3">
                <i className="fas fa-check-circle text-success" style={{ fontSize: '4rem' }}></i>
              </div>
              <h1 className="text-success mb-2">Payment Successful!</h1>
              <p className="text-muted fs-5">Thank you for your order. Your payment has been processed successfully.</p>
            </div>

            {/* Order Details Card */}
            <div className="card mb-4">
              <div className="card-header bg-success text-white">
                <h5 className="mb-0">
                  <i className="fas fa-receipt me-2"></i>
                  Order Confirmation
                </h5>
              </div>
              <div className="card-body">
                <div className="row mb-3">
                  <div className="col-md-6">
                    <strong>Order ID:</strong> #{order.id}
                  </div>
                  <div className="col-md-6">
                    <strong>Order Date:</strong> {formatDate(order.orderDate)}
                  </div>
                </div>
                <div className="row mb-3">
                  <div className="col-md-6">
                    <strong>Status:</strong> 
                    <span className="badge bg-success ms-2">Confirmed</span>
                  </div>
                  <div className="col-md-6">
                    <strong>Total Amount:</strong> 
                    <span className="text-primary fw-bold fs-5 ms-2">₹{order.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="fas fa-user me-2"></i>
                  Delivery Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6">
                    <p className="mb-1"><strong>Name:</strong> {order.customerInfo.firstName} {order.customerInfo.lastName}</p>
                    <p className="mb-1"><strong>Email:</strong> {order.customerInfo.email}</p>
                    <p className="mb-1"><strong>Phone:</strong> {order.customerInfo.phone}</p>
                  </div>
                  <div className="col-md-6">
                    <p className="mb-1"><strong>Address:</strong></p>
                    <p className="mb-0 text-muted">
                      {order.customerInfo.address}<br />
                      {order.customerInfo.city}, {order.customerInfo.state} {order.customerInfo.zipCode}<br />
                      {order.customerInfo.country}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="fas fa-box me-2"></i>
                  Order Items ({order.items.length} {order.items.length === 1 ? 'item' : 'items'})
                </h5>
              </div>
              <div className="card-body">
                {order.items.map((item, index) => (
                  <div key={item._id} className={`d-flex align-items-center py-3 ${index < order.items.length - 1 ? 'border-bottom' : ''}`}>
                    <img 
                      src={getImageUrl(item.itemImage)} 
                      className="rounded"
                      alt={item.title}
                      style={{ width: '80px', height: '100px', objectFit: 'cover' }}
                      onError={handleImageError}
                    />
                    <div className="ms-3 flex-grow-1">
                      <h6 className="mb-1">{item.title}</h6>
                      <p className="text-muted mb-1">{item.author}</p>
                      <p className="mb-0">
                        <span className="text-primary fw-bold">₹{item.price}</span>
                        <span className="text-muted"> × {item.quantity}</span>
                        <span className="ms-3 fw-bold">₹{(parseFloat(item.price) * item.quantity).toFixed(2)}</span>
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="fas fa-calculator me-2"></i>
                  Order Summary
                </h5>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-between mb-2">
                  <span>Subtotal:</span>
                  <span>₹{order.subtotal.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Tax (18% GST):</span>
                  <span>₹{order.tax.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Shipping:</span>
                  <span>₹{order.shipping.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between fw-bold fs-5 border-top pt-2">
                  <span>Total:</span>
                  <span className="text-primary">₹{order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Delivery Information */}
            <div className="alert alert-info">
              <h6 className="alert-heading">
                <i className="fas fa-truck me-2"></i>
                Delivery Information
              </h6>
              <p className="mb-2">Your order will be delivered within 5-7 business days.</p>
              <p className="mb-0">You will receive a tracking number via email once your order is shipped.</p>
            </div>

            {/* Action Buttons */}
            <div className="text-center mb-4">
              <div className="d-grid gap-2 d-md-flex justify-content-md-center">
                <button 
                  className="btn btn-primary btn-lg me-md-2"
                  onClick={() => navigate('/myorders-new')}
                >
                  <i className="fas fa-list me-2"></i>
                  View My Orders
                </button>
                <button 
                  className="btn btn-outline-primary btn-lg"
                  onClick={() => navigate('/books')}
                >
                  <i className="fas fa-shopping-bag me-2"></i>
                  Continue Shopping
                </button>
              </div>
            </div>

            {/* Thank You Message */}
            <div className="text-center">
              <div className="card bg-light">
                <div className="card-body">
                  <h5 className="card-title text-primary">Thank You for Your Purchase!</h5>
                  <p className="card-text">
                    We appreciate your business and hope you enjoy your new books. 
                    If you have any questions about your order, please don't hesitate to contact us.
                  </p>
                  <p className="card-text">
                    <small className="text-muted">
                      Order confirmation has been sent to {order.customerInfo.email}
                    </small>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
