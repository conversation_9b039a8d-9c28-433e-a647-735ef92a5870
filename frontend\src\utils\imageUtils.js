// Utility functions for handling images in the BookStore application

/**
 * Constructs the proper image URL for book covers
 * @param {string} itemImage - The image filename from the database
 * @returns {string} - The complete URL to the image
 */
export const getImageUrl = (itemImage) => {
  if (!itemImage) {
    return '/default_cover.svg';
  }

  // If itemImage is already a full URL, use it as is
  if (itemImage.startsWith('http')) {
    return itemImage;
  }

  // All images are now in the uploads folder served by backend
  // Handle spaces in filenames by encoding them
  const encodedImage = encodeURIComponent(itemImage);
  return `http://localhost:4000/uploads/${encodedImage}`;
};

/**
 * Handles image loading errors by setting a fallback image
 * @param {Event} event - The error event from the img element
 */
export const handleImageError = (event) => {
  event.target.src = '/default_cover.svg';
  event.target.onerror = null; // Prevent infinite loop if fallback also fails
};

/**
 * Preloads an image to check if it exists
 * @param {string} imageUrl - The URL of the image to preload
 * @returns {Promise<boolean>} - Promise that resolves to true if image loads successfully
 */
export const preloadImage = (imageUrl) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
};

/**
 * Gets the appropriate image URL with fallback checking
 * @param {string} itemImage - The image filename from the database
 * @returns {Promise<string>} - Promise that resolves to the working image URL
 */
export const getValidImageUrl = async (itemImage) => {
  const primaryUrl = getImageUrl(itemImage);
  const isValid = await preloadImage(primaryUrl);
  
  if (isValid) {
    return primaryUrl;
  }
  
  return '/default_cover.svg';
};

export default {
  getImageUrl,
  handleImageError,
  preloadImage,
  getValidImageUrl
};
